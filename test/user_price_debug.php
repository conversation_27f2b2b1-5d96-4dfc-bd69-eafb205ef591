<?php
/**
 * 用户价格调试页面
 * 用于诊断价格显示与数据库不一致的问题
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 检查登录状态
if ($islogin != 1) {
    die('请先登录系统');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>用户价格调试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
        .highlight { background-color: #ffeb3b; padding: 2px 4px; }
        .error { color: red; font-weight: bold; }
        .success { color: green; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .code { background: #f4f4f4; padding: 10px; border-left: 4px solid #ccc; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🔍 用户价格调试报告</h1>
    
    <div class="debug-section">
        <h2>📋 当前用户信息</h2>
        <table>
            <tr><th>字段</th><th>值</th><th>说明</th></tr>
            <tr>
                <td>用户ID (uid)</td>
                <td class="<?php echo $userrow['uid'] == 1 ? 'success' : ''; ?>"><?php echo $userrow['uid']; ?></td>
                <td><?php echo $userrow['uid'] == 1 ? '✅ 超级管理员' : '普通用户'; ?></td>
            </tr>
            <tr>
                <td>用户名 (user)</td>
                <td><?php echo htmlspecialchars($userrow['user']); ?></td>
                <td>登录账号</td>
            </tr>
            <tr>
                <td>显示名称 (name)</td>
                <td><?php echo htmlspecialchars($userrow['name']); ?></td>
                <td>用户昵称</td>
            </tr>
            <tr>
                <td>费率 (addprice)</td>
                <td class="highlight"><?php echo $userrow['addprice']; ?></td>
                <td>🔥 这是价格计算的关键参数</td>
            </tr>
            <tr>
                <td>余额 (money)</td>
                <td><?php echo $userrow['money']; ?></td>
                <td>账户余额</td>
            </tr>
            <tr>
                <td>上级用户ID (uuid)</td>
                <td><?php echo $userrow['uuid']; ?></td>
                <td>上级代理</td>
            </tr>
        </table>
    </div>

    <div class="debug-section">
        <h2>💰 价格计算逻辑分析</h2>
        <div class="code">
            <strong>当前价格计算公式：</strong><br>
            显示价格 = 数据库价格 × 用户费率<br>
            显示价格 = price × <?php echo $userrow['addprice']; ?>
        </div>
        
        <?php if ($userrow['uid'] == 1): ?>
        <div class="warning">
            ⚠️ <strong>超级管理员特殊说明：</strong><br>
            作为超级管理员(uid=1)，您的费率是 <?php echo $userrow['addprice']; ?>，这意味着：<br>
            • 如果费率是1.0，显示价格 = 数据库价格 × 1 = 数据库原价<br>
            • 如果费率不是1.0，显示价格会根据费率调整<br>
            • 超级管理员可以设置任意费率，包括小于0.2的费率
        </div>
        <?php endif; ?>
    </div>

    <div class="debug-section">
        <h2>🧮 实际价格计算示例</h2>
        <?php
        // 获取几个商品进行价格计算演示
        $sample_products = $DB->query("SELECT cid, name, price, yunsuan FROM qingka_wangke_class WHERE status=1 ORDER BY cid ASC LIMIT 5");
        ?>
        <table>
            <tr>
                <th>商品ID</th>
                <th>商品名称</th>
                <th>数据库价格</th>
                <th>运算方式</th>
                <th>计算过程</th>
                <th>显示价格</th>
            </tr>
            <?php while ($product = $DB->fetch($sample_products)): ?>
                <?php
                $db_price = $product['price'];
                $operation = $product['yunsuan'];
                $user_rate = $userrow['addprice'];
                
                if ($operation == '*') {
                    $calculated_price = $db_price * $user_rate;
                    $process = "{$db_price} × {$user_rate}";
                } elseif ($operation == '+') {
                    $calculated_price = $db_price + $user_rate;
                    $process = "{$db_price} + {$user_rate}";
                } else {
                    $calculated_price = $db_price * $user_rate;
                    $process = "{$db_price} × {$user_rate} (默认)";
                }
                
                $formatted_price = formatPrice($calculated_price);
                ?>
                <tr>
                    <td><?php echo $product['cid']; ?></td>
                    <td><?php echo htmlspecialchars($product['name']); ?></td>
                    <td><?php echo $db_price; ?></td>
                    <td><?php echo $operation ?: '*'; ?></td>
                    <td><?php echo $process; ?> = <?php echo $calculated_price; ?></td>
                    <td class="highlight"><?php echo $formatted_price; ?></td>
                </tr>
            <?php endwhile; ?>
        </table>
    </div>

    <div class="debug-section">
        <h2>🔍 问题诊断</h2>
        <?php
        $issues = [];
        $suggestions = [];
        
        // 检查费率是否合理
        if ($userrow['addprice'] == 1.0) {
            $suggestions[] = "✅ 费率为1.0，显示价格等于数据库原价，这是正常的";
        } elseif ($userrow['addprice'] > 1.0) {
            $issues[] = "⚠️ 费率大于1.0 ({$userrow['addprice']})，显示价格会高于数据库价格";
        } elseif ($userrow['addprice'] < 0.2 && $userrow['uid'] != 1) {
            $issues[] = "❌ 费率小于0.2 ({$userrow['addprice']})，这对普通用户是不正常的";
        } elseif ($userrow['addprice'] < 1.0) {
            $suggestions[] = "ℹ️ 费率小于1.0 ({$userrow['addprice']})，显示价格会低于数据库价格";
        }
        
        // 检查是否为超级管理员
        if ($userrow['uid'] == 1) {
            $suggestions[] = "👑 您是超级管理员，可以设置任意费率";
            if ($userrow['addprice'] != 1.0) {
                $suggestions[] = "💡 如果您希望看到数据库原价，请将费率设置为1.0";
            }
        }
        
        // 显示问题和建议
        if (!empty($issues)) {
            echo "<h3 class='error'>🚨 发现的问题：</h3><ul>";
            foreach ($issues as $issue) {
                echo "<li class='error'>{$issue}</li>";
            }
            echo "</ul>";
        }
        
        if (!empty($suggestions)) {
            echo "<h3 class='success'>💡 说明和建议：</h3><ul>";
            foreach ($suggestions as $suggestion) {
                echo "<li>{$suggestion}</li>";
            }
            echo "</ul>";
        }
        ?>
    </div>

    <div class="debug-section">
        <h2>🛠️ 解决方案</h2>
        
        <?php if ($userrow['uid'] == 1): ?>
        <div class="success">
            <h3>超级管理员解决方案：</h3>
            <ol>
                <li><strong>查看数据库原价：</strong> 将费率设置为1.0</li>
                <li><strong>修改费率：</strong> 在个人中心或用户管理中修改addprice字段</li>
                <li><strong>直接查看数据库：</strong> 在商品管理页面查看price字段的原始值</li>
            </ol>
            
            <div class="code">
                <strong>SQL查询数据库原价：</strong><br>
                SELECT cid, name, price FROM qingka_wangke_class WHERE status=1;
            </div>
        </div>
        <?php else: ?>
        <div class="warning">
            <h3>普通用户说明：</h3>
            <p>您看到的价格是根据您的费率计算的结果，这是正常的。如需查看其他价格，请联系管理员调整费率。</p>
        </div>
        <?php endif; ?>
    </div>

    <div class="debug-section">
        <h2>📊 费率对比表</h2>
        <p>以数据库价格1.0为例，不同费率下的显示价格：</p>
        <table>
            <tr><th>费率</th><th>显示价格</th><th>说明</th></tr>
            <tr><td>0.2</td><td>0.2</td><td>最低费率</td></tr>
            <tr><td>0.5</td><td>0.5</td><td>5折</td></tr>
            <tr><td>1.0</td><td>1.0</td><td>原价</td></tr>
            <tr><td>1.5</td><td>1.5</td><td>1.5倍价格</td></tr>
            <tr class="highlight"><td><?php echo $userrow['addprice']; ?></td><td><?php echo formatPrice(1.0 * $userrow['addprice']); ?></td><td>您的当前费率</td></tr>
        </table>
    </div>

    <div class="debug-section">
        <h2>🔗 相关页面链接</h2>
        <ul>
            <li><a href="../index/price.php" target="_blank">价格表页面</a> - 查看所有商品价格</li>
            <li><a href="../index/myprice.php" target="_blank">我的价格页面</a> - 查看个人价格</li>
            <?php if ($userrow['uid'] == 1): ?>
            <li><a href="../index/class.php" target="_blank">商品管理页面</a> - 管理商品和查看原价</li>
            <li><a href="../index/userlist.php" target="_blank">用户管理页面</a> - 管理用户费率</li>
            <?php endif; ?>
        </ul>
    </div>

    <div class="debug-section">
        <h2>📝 总结</h2>
        <div class="code">
            <strong>结论：</strong><br>
            前端显示价格 = 数据库价格 × 用户费率(<?php echo $userrow['addprice']; ?>)<br>
            这是系统的正常行为，不是bug。<br><br>
            
            <?php if ($userrow['uid'] == 1 && $userrow['addprice'] != 1.0): ?>
            <span class="warning">
            如果您希望看到数据库原价，请将您的费率设置为1.0。
            </span>
            <?php else: ?>
            <span class="success">
            当前价格显示是正确的。
            </span>
            <?php endif; ?>
        </div>
    </div>

</body>
</html>
