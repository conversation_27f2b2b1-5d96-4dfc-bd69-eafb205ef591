<?php
/**
 * 分类显示映射测试页面
 * 验证分类名称映射功能是否正常工作
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 检查登录状态
if ($islogin != 1) {
    die('请先登录系统');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>分类显示映射测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background-color: #ffeb3b; padding: 2px 4px; }
        .admin-badge { background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px; }
        .user-badge { background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🧪 分类显示映射测试</h1>
    
    <div class="test-section info">
        <h2>📋 当前用户信息</h2>
        <table>
            <tr>
                <th>用户ID</th>
                <th>用户名</th>
                <th>权限级别</th>
                <th>显示模式</th>
            </tr>
            <tr>
                <td><?php echo $userrow['uid']; ?></td>
                <td><?php echo htmlspecialchars($userrow['user']); ?></td>
                <td>
                    <?php if ($userrow['uid'] == 1): ?>
                        <span class="admin-badge">超级管理员</span>
                    <?php else: ?>
                        <span class="user-badge">普通用户</span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php if ($userrow['uid'] == 1): ?>
                        <strong>原始名称</strong>（便于管理）
                    <?php else: ?>
                        <strong>映射名称</strong>（继教1、继教2、继教3）
                    <?php endif; ?>
                </td>
            </tr>
        </table>
    </div>

    <div class="test-section">
        <h2>🔄 映射配置测试</h2>
        <table>
            <tr>
                <th>原始分类名称</th>
                <th>映射后名称</th>
                <th>您看到的名称</th>
                <th>测试状态</th>
            </tr>
            <?php
            $test_categories = ['yyy', 'yyy教育', '8090edu', '8090教育', '易教育'];
            $expected_mapping = [
                'yyy' => '继教1',
                'yyy教育' => '继教1', 
                '8090edu' => '继教2',
                '8090教育' => '继教2',
                '易教育' => '继教3'
            ];
            
            foreach ($test_categories as $original_name) {
                $mapped_name = $expected_mapping[$original_name] ?? $original_name;
                $display_name = getCategoryDisplayName($original_name);
                
                // 判断测试结果
                if ($userrow['uid'] == 1) {
                    // 超级管理员应该看到原始名称
                    $test_passed = ($display_name === $original_name);
                    $expected = $original_name;
                } else {
                    // 普通用户应该看到映射名称
                    $test_passed = ($display_name === $mapped_name);
                    $expected = $mapped_name;
                }
                
                $status_class = $test_passed ? 'success' : 'warning';
                $status_text = $test_passed ? '✅ 通过' : '❌ 失败';
            ?>
            <tr class="<?php echo $status_class; ?>">
                <td><?php echo htmlspecialchars($original_name); ?></td>
                <td><?php echo htmlspecialchars($mapped_name); ?></td>
                <td class="highlight"><?php echo htmlspecialchars($display_name); ?></td>
                <td><?php echo $status_text; ?></td>
            </tr>
            <?php } ?>
        </table>
    </div>

    <div class="test-section">
        <h2>📊 数据库分类状态</h2>
        <table>
            <tr>
                <th>分类ID</th>
                <th>数据库原名称</th>
                <th>显示名称</th>
                <th>商品数量</th>
                <th>状态</th>
            </tr>
            <?php
            $categories = $DB->query("
                SELECT 
                    f.id, 
                    f.name, 
                    f.status,
                    COUNT(c.cid) as product_count
                FROM qingka_wangke_fenlei f
                LEFT JOIN qingka_wangke_class c ON f.id = c.fenlei
                WHERE f.status = 1
                GROUP BY f.id, f.name, f.status
                ORDER BY f.id ASC
            ");
            
            while ($cat = $DB->fetch($categories)):
                $display_name = getCategoryDisplayName($cat['name']);
                $is_mapped = ($display_name !== $cat['name']);
            ?>
            <tr <?php echo $is_mapped ? 'style="background-color: #fff3e0;"' : ''; ?>>
                <td><?php echo $cat['id']; ?></td>
                <td><?php echo htmlspecialchars($cat['name']); ?></td>
                <td class="highlight"><?php echo htmlspecialchars($display_name); ?></td>
                <td><?php echo $cat['product_count']; ?></td>
                <td>
                    <?php if ($is_mapped): ?>
                        <span style="color: #ff9800;">🔄 已映射</span>
                    <?php else: ?>
                        <span style="color: #666;">📝 原名称</span>
                    <?php endif; ?>
                </td>
            </tr>
            <?php endwhile; ?>
        </table>
    </div>

    <div class="test-section">
        <h2>🔧 功能验证清单</h2>
        <table>
            <tr>
                <th>功能项目</th>
                <th>测试方法</th>
                <th>预期结果</th>
                <th>操作</th>
            </tr>
            <tr>
                <td>分类表显示</td>
                <td>访问分类表页面</td>
                <td>
                    <?php if ($userrow['uid'] == 1): ?>
                        显示原始名称
                    <?php else: ?>
                        显示继教1、继教2、继教3
                    <?php endif; ?>
                </td>
                <td><a href="../index/fenleibiao.php" target="_blank">测试</a></td>
            </tr>
            <tr>
                <td>下单页面分类</td>
                <td>访问下单页面</td>
                <td>分类按钮显示映射名称</td>
                <td><a href="../index/add1.php" target="_blank">测试</a></td>
            </tr>
            <tr>
                <td>价格表分类</td>
                <td>访问价格表页面</td>
                <td>分类列显示映射名称</td>
                <td><a href="../index/price.php" target="_blank">测试</a></td>
            </tr>
            <tr>
                <td>分类管理</td>
                <td>访问分类管理页面</td>
                <td>根据权限显示不同名称</td>
                <td><a href="../index/fenlei.php" target="_blank">测试</a></td>
            </tr>
            <tr>
                <td>API同步功能</td>
                <td>运行同步脚本</td>
                <td>功能正常，不受影响</td>
                <td>
                    <a href="../api/yyy.php?pricee=3" target="_blank">YYY</a> |
                    <a href="../api/8090edu.php?pricee=3" target="_blank">8090</a> |
                    <a href="../api/jxjy.php?pricee=5" target="_blank">易教育</a>
                </td>
            </tr>
        </table>
    </div>

    <div class="test-section <?php echo ($userrow['uid'] == 1) ? 'warning' : 'success'; ?>">
        <h2>💡 使用说明</h2>
        <?php if ($userrow['uid'] == 1): ?>
        <h3>🔧 超级管理员模式</h3>
        <ul>
            <li><strong>您看到的是原始名称</strong>，便于管理和调试</li>
            <li>所有API同步脚本正常工作，因为使用的是原始名称</li>
            <li>如需查看普通用户视图，请使用其他账号登录</li>
            <li>可以随时修改映射配置文件调整显示规则</li>
        </ul>
        <?php else: ?>
        <h3>👤 普通用户模式</h3>
        <ul>
            <li><strong>您看到的是映射后的名称</strong>：继教1、继教2、继教3</li>
            <li>这只是显示效果，不影响系统功能</li>
            <li>所有下单、查课等功能完全正常</li>
            <li>管理员可以随时调整显示规则</li>
        </ul>
        <?php endif; ?>
    </div>

    <div class="test-section info">
        <h2>🎯 映射规则</h2>
        <table>
            <tr>
                <th>显示名称</th>
                <th>对应原始名称</th>
                <th>平台说明</th>
            </tr>
            <tr>
                <td><strong>继教1</strong></td>
                <td>yyy、yyy教育</td>
                <td>YYY教育平台（1175+网站）</td>
            </tr>
            <tr>
                <td><strong>继教2</strong></td>
                <td>8090edu、8090教育</td>
                <td>8090教育平台（533个网站）</td>
            </tr>
            <tr>
                <td><strong>继教3</strong></td>
                <td>易教育</td>
                <td>易教育平台（聚合多个教育网站）</td>
            </tr>
        </table>
    </div>

</body>
</html>
