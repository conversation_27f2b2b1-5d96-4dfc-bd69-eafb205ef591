<?php
/**
 * 下单页面性能测试脚本
 * 测试优化前后的性能差异
 */

include(__DIR__ . '/../confing/common.php');

// 性能测试函数
function performanceTest($testName, $callback, $iterations = 5) {
    echo "\n=== {$testName} ===\n";
    $times = [];
    
    for ($i = 0; $i < $iterations; $i++) {
        $start = microtime(true);
        $result = $callback();
        $end = microtime(true);
        
        $time = ($end - $start) * 1000; // 转换为毫秒
        $times[] = $time;
        echo "第" . ($i + 1) . "次: " . number_format($time, 2) . "ms\n";
    }
    
    $avgTime = array_sum($times) / count($times);
    $minTime = min($times);
    $maxTime = max($times);
    
    echo "平均时间: " . number_format($avgTime, 2) . "ms\n";
    echo "最快时间: " . number_format($minTime, 2) . "ms\n";
    echo "最慢时间: " . number_format($maxTime, 2) . "ms\n";
    
    return $avgTime;
}

// 测试1：原始查询方式（模拟优化前）
function testOriginalQuery($DB, $userrow) {
    $fenlei = '104'; // 8090edu分类
    $sql = "SELECT cid,name,price,content,fenlei,yunsuan FROM qingka_wangke_class WHERE status=1 AND fenlei=? ORDER BY cid ASC LIMIT 50";
    $stmt = $DB->prepare_query($sql, [$fenlei]);
    
    $data = [];
    if ($stmt) {
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            // 模拟原始的N+1查询
            $mijia = $DB->prepare_getrow("SELECT price,mode FROM qingka_wangke_mijia WHERE uid=? AND cid=?", [$userrow['uid'], $row['cid']]);
            
            $price = calculateUserPrice($row['price'], $userrow['addprice'], $row['yunsuan']);
            $data[] = [
                'cid' => $row['cid'],
                'name' => $row['name'],
                'price' => $price,
                'has_mijia' => $mijia ? true : false
            ];
        }
        $stmt->close();
    }
    
    return count($data);
}

// 测试2：优化后的查询方式
function testOptimizedQuery($DB, $userrow) {
    $fenlei = '104'; // 8090edu分类
    $sql = "SELECT c.cid, c.name, c.price, c.content, c.fenlei, c.yunsuan,
                   m.price as mijia_price, m.mode as mijia_mode
            FROM qingka_wangke_class c
            LEFT JOIN qingka_wangke_mijia m ON m.uid = ? AND m.cid = c.cid
            WHERE c.status=1 AND c.fenlei=? ORDER BY c.cid ASC LIMIT 50";
    
    $stmt = $DB->prepare_query($sql, [$userrow['uid'], $fenlei]);
    
    $data = [];
    if ($stmt) {
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $price = calculateUserPrice($row['price'], $userrow['addprice'], $row['yunsuan']);
            $data[] = [
                'cid' => $row['cid'],
                'name' => $row['name'],
                'price' => $price,
                'has_mijia' => $row['mijia_price'] ? true : false
            ];
        }
        $stmt->close();
    }
    
    return count($data);
}

// 测试3：缓存测试
function testCachePerformance($redis) {
    $cache_key = "test_cache_" . time();
    $test_data = ['code' => 1, 'data' => array_fill(0, 100, ['id' => 1, 'name' => 'test'])];
    $json_data = json_encode($test_data);
    
    // 写入缓存
    $start = microtime(true);
    $redis->setex($cache_key, 300, $json_data);
    $write_time = (microtime(true) - $start) * 1000;
    
    // 读取缓存
    $start = microtime(true);
    $cached = $redis->get($cache_key);
    $read_time = (microtime(true) - $start) * 1000;
    
    // 清理
    $redis->del($cache_key);
    
    echo "缓存写入: " . number_format($write_time, 2) . "ms\n";
    echo "缓存读取: " . number_format($read_time, 2) . "ms\n";
    
    return $read_time;
}

// 开始测试
echo "下单页面性能测试开始...\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n";

// 获取测试用户
$userrow = $DB->get_row("SELECT * FROM qingka_wangke_user WHERE uid=1 LIMIT 1");
if (!$userrow) {
    die("找不到测试用户\n");
}

echo "测试用户: {$userrow['user']} (UID: {$userrow['uid']})\n";

// 数据库连接测试
$start = microtime(true);
$count = $DB->count("SELECT COUNT(*) FROM qingka_wangke_class WHERE status=1");
$db_time = (microtime(true) - $start) * 1000;
echo "数据库连接测试: " . number_format($db_time, 2) . "ms (共{$count}个项目)\n";

// 测试原始查询
$originalTime = performanceTest("原始查询方式（N+1问题）", function() use ($DB, $userrow) {
    return testOriginalQuery($DB, $userrow);
});

// 测试优化查询
$optimizedTime = performanceTest("优化后查询方式（JOIN）", function() use ($DB, $userrow) {
    return testOptimizedQuery($DB, $userrow);
});

// 测试缓存性能
performanceTest("Redis缓存性能", function() use ($redis) {
    return testCachePerformance($redis);
});

// 计算性能提升
$improvement = (($originalTime - $optimizedTime) / $originalTime) * 100;
echo "\n=== 性能对比结果 ===\n";
echo "原始查询平均时间: " . number_format($originalTime, 2) . "ms\n";
echo "优化查询平均时间: " . number_format($optimizedTime, 2) . "ms\n";
echo "性能提升: " . number_format($improvement, 1) . "%\n";

if ($improvement > 0) {
    echo "✅ 优化成功！查询速度提升了 " . number_format($improvement, 1) . "%\n";
} else {
    echo "❌ 优化效果不明显，可能需要进一步调整\n";
}

// 内存使用情况
echo "\n=== 系统资源使用 ===\n";
echo "内存使用: " . number_format(memory_get_usage() / 1024 / 1024, 2) . "MB\n";
echo "峰值内存: " . number_format(memory_get_peak_usage() / 1024 / 1024, 2) . "MB\n";

echo "\n测试完成！\n";
?>
