<?php
/**
 * 分类名称修改影响分析工具
 * 分析修改分类名称对系统功能的影响
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 检查登录状态
if ($islogin != 1) {
    die('请先登录系统');
}

// 检查超级管理员权限
if ($userrow['uid'] != 1) {
    die('仅超级管理员可访问此页面');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>分类名称修改影响分析</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .analysis-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
        .impact-high { border-left: 4px solid #f44336; background: #ffebee; }
        .impact-medium { border-left: 4px solid #ff9800; background: #fff3e0; }
        .impact-low { border-left: 4px solid #4caf50; background: #e8f5e9; }
        .impact-none { border-left: 4px solid #9e9e9e; background: #f5f5f5; }
        .code-block { background: #f4f4f4; padding: 10px; border-left: 4px solid #ccc; font-family: monospace; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background-color: #ffeb3b; padding: 2px 4px; }
        .warning { color: #f44336; font-weight: bold; }
        .safe { color: #4caf50; font-weight: bold; }
        .caution { color: #ff9800; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔍 分类名称修改影响分析</h1>
    
    <div class="analysis-section">
        <h2>📊 当前分类状态</h2>
        <table>
            <thead>
                <tr>
                    <th>分类ID</th>
                    <th>当前名称</th>
                    <th>商品数量</th>
                    <th>平台类型</th>
                    <th>关联货源</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // 查询所有分类及其商品数量
                $categories = $DB->query("
                    SELECT 
                        f.id, 
                        f.name, 
                        f.status,
                        COUNT(c.cid) as product_count
                    FROM qingka_wangke_fenlei f
                    LEFT JOIN qingka_wangke_class c ON f.id = c.fenlei
                    WHERE f.status = 1
                    GROUP BY f.id, f.name, f.status
                    ORDER BY f.id ASC
                ");
                
                while ($cat = $DB->fetch($categories)):
                    // 检查是否为关键分类
                    $is_key_category = false;
                    $platform_type = '普通分类';
                    $related_sources = [];
                    
                    if (stripos($cat['name'], '易教育') !== false || stripos($cat['name'], 'jxjy') !== false) {
                        $is_key_category = true;
                        $platform_type = '易教育平台';
                        $sources = $DB->query("SELECT hid, name FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育')");
                        while ($source = $DB->fetch($sources)) {
                            $related_sources[] = $source['name'];
                        }
                    } elseif (stripos($cat['name'], 'yyy') !== false) {
                        $is_key_category = true;
                        $platform_type = 'YYY教育平台';
                        $sources = $DB->query("SELECT hid, name FROM qingka_wangke_huoyuan WHERE instr(pt,'yyy') OR instr(name,'yyy')");
                        while ($source = $DB->fetch($sources)) {
                            $related_sources[] = $source['name'];
                        }
                    } elseif (stripos($cat['name'], '8090edu') !== false || stripos($cat['name'], '8090') !== false) {
                        $is_key_category = true;
                        $platform_type = '8090教育平台';
                        $sources = $DB->query("SELECT hid, name FROM qingka_wangke_huoyuan WHERE instr(pt,'8090edu') OR instr(name,'8090edu')");
                        while ($source = $DB->fetch($sources)) {
                            $related_sources[] = $source['name'];
                        }
                    }
                    
                    $row_class = $is_key_category ? 'style="background-color: #fff3e0;"' : '';
                ?>
                <tr <?php echo $row_class; ?>>
                    <td><?php echo $cat['id']; ?></td>
                    <td>
                        <?php echo htmlspecialchars($cat['name']); ?>
                        <?php if ($is_key_category): ?>
                            <span class="warning">⚠️ 关键分类</span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo $cat['product_count']; ?></td>
                    <td><?php echo $platform_type; ?></td>
                    <td><?php echo implode(', ', $related_sources); ?></td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>

    <div class="analysis-section impact-high">
        <h2>🚨 高风险影响 - 会导致功能异常</h2>
        <h3>1. API同步脚本失效</h3>
        <p><strong>影响文件：</strong></p>
        <ul>
            <li><code>api/jxjy.php</code> - 易教育同步脚本</li>
            <li><code>api/yyy.php</code> - YYY教育同步脚本</li>
            <li><code>api/8090edu.php</code> - 8090教育同步脚本</li>
            <li><code>api/8090edu_smart_sync.php</code> - 8090教育智能同步</li>
            <li><code>api/8090edu_simple_sync.php</code> - 8090教育简化同步</li>
        </ul>
        
        <h4>问题代码示例：</h4>
        <div class="code-block">
// 易教育同步脚本中的查询
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'易教育') ORDER BY id DESC LIMIT 0, 1");

// YYY教育同步脚本中的查询  
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'yyy') ORDER BY id DESC LIMIT 0, 1");

// 8090教育同步脚本中的查询
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'8090edu') ORDER BY id DESC LIMIT 0, 1");
        </div>
        
        <p class="warning">⚠️ 如果修改分类名称，这些脚本将无法找到对应分类，导致同步失败！</p>
    </div>

    <div class="analysis-section impact-high">
        <h2>🚨 高风险影响 - API接口异常</h2>
        <h3>2. 易教育API接口失效</h3>
        <p><strong>影响文件：</strong></p>
        <ul>
            <li><code>api/jxjyapi.php</code> - 易教育API接口</li>
            <li><code>api/jxjyapi_complex.php</code> - 易教育复杂API</li>
            <li><code>api/jxjyapi_backup.php</code> - 易教育备份API</li>
            <li><code>apisub.php</code> - 主要API处理文件</li>
        </ul>
        
        <h4>问题代码示例：</h4>
        <div class="code-block">
// 易教育货源查询
$jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");

// 易教育项目特殊处理
$jxjy_huoyuan = $DB->get_row("SELECT hid FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
        </div>
    </div>

    <div class="analysis-section impact-medium">
        <h2>⚠️ 中等风险影响 - 管理功能受限</h2>
        <h3>3. 管理脚本和工具</h3>
        <p><strong>影响文件：</strong></p>
        <ul>
            <li><code>update_jxjy_api.php</code> - 易教育API更新脚本</li>
            <li><code>update_jxjy_content.php</code> - 易教育内容更新脚本</li>
            <li><code>update_existing_orders.php</code> - 订单更新脚本</li>
            <li><code>remove_yyy_prefix.php</code> - YYY前缀清理脚本</li>
        </ul>
    </div>

    <div class="analysis-section impact-low">
        <h2>✅ 低风险影响 - 仅显示效果</h2>
        <h3>4. 前端显示页面</h3>
        <p><strong>影响文件：</strong></p>
        <ul>
            <li><code>index/fenleibiao.php</code> - 分类表显示</li>
            <li><code>index/fenlei.php</code> - 分类管理</li>
            <li><code>index/add1.php</code> - 下单页面分类选择</li>
        </ul>
        <p class="safe">✅ 这些只影响显示效果，不影响核心功能</p>
    </div>

    <div class="analysis-section impact-none">
        <h2>✅ 无风险影响 - 安全修改</h2>
        <h3>5. 可以安全修改的内容</h3>
        <ul>
            <li>分类的描述信息 (text字段)</li>
            <li>分类的排序 (sort字段)</li>
            <li>分类的状态 (status字段)</li>
            <li>非关键分类的名称</li>
        </ul>
    </div>

    <div class="analysis-section">
        <h2>🛠️ 安全修改方案</h2>
        
        <h3>方案1: 保持关键字不变（推荐）</h3>
        <div class="code-block">
原名称: "易教育" → 建议名称: "易教育平台" 或 "易教育网课"
原名称: "yyy" → 建议名称: "yyy教育" 或 "yyy网课平台"  
原名称: "8090edu" → 建议名称: "8090教育" 或 "8090edu平台"
        </div>
        <p class="safe">✅ 这样修改不会影响系统功能，因为instr()函数仍能匹配到关键字</p>
        
        <h3>方案2: 修改代码适配新名称</h3>
        <p class="caution">⚠️ 如果一定要完全修改名称，需要同时修改以下文件中的查询条件：</p>
        <ol>
            <li>所有API同步脚本中的分类查询</li>
            <li>所有API接口文件中的货源查询</li>
            <li>相关的管理脚本和工具</li>
        </ol>
        
        <h3>方案3: 使用分类ID而非名称</h3>
        <p>最安全的方法是修改代码，使用固定的分类ID而不是名称匹配：</p>
        <div class="code-block">
// 修改前（不安全）
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE instr(name ,'易教育') ORDER BY id DESC LIMIT 0, 1");

// 修改后（安全）
$b = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE id = '3' LIMIT 1"); // 假设易教育分类ID是3
        </div>
    </div>

    <div class="analysis-section">
        <h2>📋 修改前检查清单</h2>
        <table>
            <tr>
                <th>检查项目</th>
                <th>状态</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>备份数据库</td>
                <td class="warning">必须</td>
                <td>修改前务必备份整个数据库</td>
            </tr>
            <tr>
                <td>测试API同步</td>
                <td class="warning">必须</td>
                <td>修改后测试所有API同步脚本</td>
            </tr>
            <tr>
                <td>检查订单功能</td>
                <td class="caution">建议</td>
                <td>确保下单和查课功能正常</td>
            </tr>
            <tr>
                <td>验证前端显示</td>
                <td class="safe">可选</td>
                <td>检查分类显示是否正常</td>
            </tr>
        </table>
    </div>

    <div class="analysis-section">
        <h2>🎯 推荐操作</h2>
        <div class="safe">
            <h3>✅ 最安全的做法：</h3>
            <ol>
                <li><strong>保持关键字不变</strong>：在现有名称基础上添加描述词</li>
                <li><strong>测试环境验证</strong>：先在测试环境修改并验证功能</li>
                <li><strong>逐步修改</strong>：一次只修改一个分类，确认无问题后再修改下一个</li>
                <li><strong>监控日志</strong>：修改后密切关注系统日志和错误信息</li>
            </ol>
        </div>
        
        <div class="warning">
            <h3>❌ 不建议的做法：</h3>
            <ul>
                <li>完全删除关键字（如将"易教育"改为"在线课程"）</li>
                <li>同时修改多个关键分类</li>
                <li>不备份直接在生产环境修改</li>
                <li>修改后不测试相关功能</li>
            </ul>
        </div>
    </div>

    <div class="analysis-section">
        <h2>🔗 相关工具链接</h2>
        <ul>
            <li><a href="../index/fenlei.php" target="_blank">分类管理页面</a> - 修改分类名称</li>
            <li><a href="../api/jxjy.php?pricee=5" target="_blank">测试易教育同步</a> - 验证易教育功能</li>
            <li><a href="../api/yyy.php?pricee=3" target="_blank">测试YYY同步</a> - 验证YYY功能</li>
            <li><a href="../api/8090edu.php?pricee=3" target="_blank">测试8090教育同步</a> - 验证8090教育功能</li>
        </ul>
    </div>

</body>
</html>
