<?php
/**
 * 价格格式化测试页面
 * 用于验证价格计算和显示是否正确
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 测试数据
$test_cases = [
    ['base_price' => 0.265, 'user_rate' => 0.2, 'expected' => 0.053],
    ['base_price' => 0.265, 'user_rate' => 0.25, 'expected' => 0.066],
    ['base_price' => 0.265, 'user_rate' => 0.3, 'expected' => 0.080],
    ['base_price' => 1.5, 'user_rate' => 0.2, 'expected' => 0.3],
    ['base_price' => 2.333, 'user_rate' => 0.15, 'expected' => 0.35],
    ['base_price' => 0.1, 'user_rate' => 0.5, 'expected' => 0.05],
];

?>
<!DOCTYPE html>
<html>
<head>
    <title>价格格式化测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>价格格式化测试</h1>
    
    <div class="info">
        <h3>测试说明</h3>
        <p>此页面用于测试价格计算和格式化功能，确保浮点数精度问题得到正确处理。</p>
        <p>测试包括：PHP后端计算、JavaScript前端计算、以及实际显示效果。</p>
    </div>

    <h2>PHP后端测试</h2>
    <table>
        <thead>
            <tr>
                <th>基础价格</th>
                <th>用户费率</th>
                <th>原始计算结果</th>
                <th>格式化后结果</th>
                <th>期望结果</th>
                <th>测试状态</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($test_cases as $case): ?>
                <?php
                $raw_result = $case['base_price'] * $case['user_rate'];
                $formatted_result = formatPrice($case['base_price'] * $case['user_rate']);
                $is_correct = abs($formatted_result - $case['expected']) < 0.001;
                ?>
                <tr class="<?php echo $is_correct ? 'success' : 'error'; ?>">
                    <td><?php echo $case['base_price']; ?></td>
                    <td><?php echo $case['user_rate']; ?></td>
                    <td><?php echo $raw_result; ?></td>
                    <td><?php echo $formatted_result; ?></td>
                    <td><?php echo $case['expected']; ?></td>
                    <td><?php echo $is_correct ? '✅ 通过' : '❌ 失败'; ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <h2>JavaScript前端测试</h2>
    <table>
        <thead>
            <tr>
                <th>基础价格</th>
                <th>用户费率</th>
                <th>原始计算结果</th>
                <th>格式化后结果</th>
                <th>期望结果</th>
                <th>测试状态</th>
            </tr>
        </thead>
        <tbody id="js-test-results">
            <!-- JavaScript测试结果将在这里显示 -->
        </tbody>
    </table>

    <h2>实时价格计算测试</h2>
    <div class="test-case">
        <label>基础价格: <input type="number" id="base-price" value="0.265" step="0.001"></label><br><br>
        <label>用户费率: <input type="number" id="user-rate" value="0.2" step="0.01"></label><br><br>
        <button onclick="calculateRealTime()">计算</button><br><br>
        <div id="real-time-result"></div>
    </div>

    <script src="../assets/js/price-formatter.js"></script>
    <script>
        // JavaScript测试用例
        const testCases = <?php echo json_encode($test_cases); ?>;
        
        // 运行JavaScript测试
        function runJavaScriptTests() {
            const tbody = document.getElementById('js-test-results');
            tbody.innerHTML = '';
            
            testCases.forEach(testCase => {
                const rawResult = testCase.base_price * testCase.user_rate;
                const formattedResult = formatPrice(testCase.base_price * testCase.user_rate);
                const isCorrect = Math.abs(formattedResult - testCase.expected) < 0.001;
                
                const row = document.createElement('tr');
                row.className = isCorrect ? 'success' : 'error';
                row.innerHTML = `
                    <td>${testCase.base_price}</td>
                    <td>${testCase.user_rate}</td>
                    <td>${rawResult}</td>
                    <td>${formattedResult}</td>
                    <td>${testCase.expected}</td>
                    <td>${isCorrect ? '✅ 通过' : '❌ 失败'}</td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 实时计算测试
        function calculateRealTime() {
            const basePrice = parseFloat(document.getElementById('base-price').value);
            const userRate = parseFloat(document.getElementById('user-rate').value);
            
            const rawResult = basePrice * userRate;
            const formattedResult = formatPrice(basePrice * userRate);
            
            document.getElementById('real-time-result').innerHTML = `
                <strong>计算结果：</strong><br>
                原始结果: ${rawResult}<br>
                格式化结果: ${formattedResult}<br>
                <span style="color: ${rawResult !== formattedResult ? 'red' : 'green'};">
                    ${rawResult !== formattedResult ? '需要格式化' : '无需格式化'}
                </span>
            `;
        }
        
        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            runJavaScriptTests();
            calculateRealTime();
        });
    </script>
</body>
</html>
