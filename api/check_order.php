<?php
/**
 * 订单查询API接口
 * 提供JSON格式的订单查询服务
 */

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// 引入数据库配置
include('../confing/mysqlset.php');

// 创建数据库连接
$mysqli = new mysqli($host, $user, $pwd, $dbname, $port);
if ($mysqli->connect_error) {
    http_response_code(500);
    echo json_encode([
        'code' => -1,
        'msg' => '数据库连接失败',
        'data' => null
    ]);
    exit;
}
$mysqli->set_charset("utf8");

// 获取请求参数
$username = '';
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $username = isset($input['username']) ? trim($input['username']) : '';
    
    // 如果JSON解析失败，尝试从POST参数获取
    if (empty($username) && isset($_POST['username'])) {
        $username = trim($_POST['username']);
    }
} else if ($method === 'GET') {
    $username = isset($_GET['username']) ? trim($_GET['username']) : '';
}

// 验证参数
if (empty($username)) {
    echo json_encode([
        'code' => -1,
        'msg' => '请提供要查询的账号',
        'data' => null
    ]);
    exit;
}

try {
    // 查询订单信息
    $sql = "SELECT oid, ptname, school, name, user, kcname, status, process, remarks, addtime, 
                   courseStartTime, courseEndTime, examStartTime, examEndTime, fees
            FROM qingka_wangke_order 
            WHERE user = ? 
            ORDER BY oid DESC 
            LIMIT 100";
    
    $stmt = $mysqli->prepare($sql);
    if (!$stmt) {
        throw new Exception('SQL准备失败: ' . $mysqli->error);
    }
    
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $orders = [];
    while ($row = $result->fetch_assoc()) {
        // 处理进度数据
        $process = $row['process'];
        if (!empty($process) && !strpos($process, '%')) {
            $process .= '%';
        }
        
        // 格式化订单数据
        $orders[] = [
            'id' => (int)$row['oid'],
            'platform' => $row['ptname'],
            'school' => $row['school'] ?: '',
            'name' => $row['name'] ?: '',
            'username' => $row['user'],
            'course_name' => $row['kcname'],
            'status' => $row['status'],
            'progress' => $process,
            'remarks' => $row['remarks'] ?: '',
            'order_time' => $row['addtime'],
            'course_start_time' => $row['courseStartTime'] ?: '',
            'course_end_time' => $row['courseEndTime'] ?: '',
            'exam_start_time' => $row['examStartTime'] ?: '',
            'exam_end_time' => $row['examEndTime'] ?: '',
            'fees' => $row['fees'] ? (float)$row['fees'] : 0
        ];
    }
    
    $stmt->close();
    
    // 返回结果
    if (empty($orders)) {
        echo json_encode([
            'code' => 0,
            'msg' => '未找到该账号的订单信息',
            'data' => []
        ]);
    } else {
        echo json_encode([
            'code' => 1,
            'msg' => '查询成功',
            'data' => $orders,
            'total' => count($orders)
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => -1,
        'msg' => '查询失败: ' . $e->getMessage(),
        'data' => null
    ]);
} finally {
    $mysqli->close();
}
?>
