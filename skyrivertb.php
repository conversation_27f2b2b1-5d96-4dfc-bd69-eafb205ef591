<!--skyriver天河批量进度同步 五分钟或十分钟一轮 记得联系我给你IP加白或者工单提交-->
<?php
include('confing/common.php');
$filename = 'lasttimestamp.dat';
#记录并获得上一次同步时间
if (!file_exists($filename)) {
   $lasttimestamp = intval(time())-300;
}
else{
    $lasttimestamp = file_get_contents($filename)-60;
}
file_put_contents($filename, intval(time()));
#记录并获得上一次同步时间
$offset=0;
$uid='填写你的uid';
$key='填写你的key';
while(true){
    $data = array("uid" => $uid,"key" => "$key","offset" => "$offset","timestamp" => "$lasttimestamp");
    #post请求
    $result=get_url("http://skyriver.top/api.php?act=pljd",$data);
    $result = json_decode($result, true);
    $data=$result["data"];
    $num=count($data);
    $offset+=5000;
    for($i=0;$i<count($data);$i++){
        #此处为把进度更新到数据库逻辑 可自行修改
             $DB->query("update qingka_wangke_order set `yid`='{$data[$i]['id']}',`status`='{$data[$i]['status']}',`remarks`='{$data[$i]['remarks']}',`process`='{$data[$i]['process']}',`courseStartTime`='{$data[$i]['kcks']}',`courseEndTime`='{$data[$i]['kcjs']}',`examStartTime`='{$data[$i]['ksks']}',`examEndTime`='{$data[$i]['ksjs']}' where `user`='{$data[$i]['user']}' and `pass`='{$data[$i]['pass']}' and `kcname`='{$data[$i]['kcname']}' "); 
       }
       echo "已同步".$num."条  ".date('H:i:s')."</br>";
    if($num<5000){break;}
    else{
        //超过5000条则进入循环
        continue;
    }
}

?>