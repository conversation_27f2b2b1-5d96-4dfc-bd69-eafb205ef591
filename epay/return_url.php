<?php
@header('Content-Type: text/html; charset=UTF-8');
include("../confing/common.php");
require_once("epay/notify.class.php");

$alipayNotify = new AlipayNotify($alipay_config);
$verify_result = $alipayNotify->verifyReturn();

if($verify_result) {
    $out_trade_no = $_GET['out_trade_no'];
    $out_trade_no = preg_replace('/[^0-9]/', '', $out_trade_no);
    $trade_status = $_GET['trade_status'];
    $money = floatval($_GET['money']);

    // 查询订单状态（不使用锁，因为这里只是查询显示）
    $sql = "SELECT * FROM qingka_wangke_pay WHERE out_trade_no=? LIMIT 1";
    $srow = $DB->prepare_getrow($sql, [$out_trade_no]);

    if (!$srow) {
        exit("<script language='javascript'>alert('订单不存在！');window.location.href='../index/pay';</script>");
    }

    // 获取用户信息用于显示
    $sql = "SELECT * FROM qingka_wangke_user WHERE uid=?";
    $userrow = $DB->prepare_getrow($sql, [$srow['uid']]);

    if (!$userrow) {
        exit("<script language='javascript'>alert('用户信息异常！');window.location.href='../index/pay';</script>");
    }

    // 计算赠送金额用于显示消息
    $money3 = 0;
    /*
    if ($money >= 50 && $money < 100) {
        $money3 = $money * 0.02;
    } elseif ($money >= 100 && $money < 300) {
        $money3 = $money * 0.05;
    } elseif ($money >= 300 && $money < 500) {
        $money3 = $money * 0.08;
    } elseif ($money >= 500) {
        $money3 = $money * 0.10;
    }
    */

    // 根据订单状态显示不同消息
    if($trade_status == 'TRADE_FINISHED' || $trade_status == 'TRADE_SUCCESS') {

        if($srow['status'] == 1) {
            // 订单已处理成功
            if ($money3 > 0) {
                $cg = "充值成功！您充值了{$money}积分，赠送了{$money3}积分！";
            } else {
                $cg = "充值成功！您充值了{$money}积分！";
            }
            exit("<script language='javascript'>alert('$cg');window.location.href='../index/pay';</script>");

        } else {
            // 订单还在处理中
            exit("<script language='javascript'>alert('充值处理中，请稍后查看余额变化！');window.location.href='../index/pay';</script>");
        }

    } else {
        // 支付状态异常
        exit("<script language='javascript'>alert('支付状态异常：{$trade_status}');window.location.href='../index/pay';</script>");
    }

} else {
    // 验签失败
    exit("<script language='javascript'>alert('充值验证失败！');window.location.href='../index/index';</script>");
}
?>