<?php
@header('Content-Type: text/html; charset=UTF-8');
include("../confing/common.php");
require_once("epay/notify.class.php");

$alipayNotify = new AlipayNotify($alipay_config);
$verify_result = $alipayNotify->verifyNotify();

if($verify_result) {
    // 基础安全检查
    $required_params = ['out_trade_no', 'trade_status', 'money', 'sign'];
    foreach($required_params as $param) {
        if(!isset($_GET[$param]) || empty($_GET[$param])) {
            echo "fail";
            exit;
        }
    }

    $out_trade_no = $_GET['out_trade_no'];
    $out_trade_no = preg_replace('/[^0-9]/', '', $out_trade_no);
    $trade_no = $_GET['trade_no'];
    $trade_status = $_GET['trade_status'];
    $money = floatval($_GET['money']);
    $type = $_GET['type'];

    // 金额安全检查
    if($money <= 0 || $money > 10000) {
        echo "fail";
        exit;
    }

    // 使用事务确保数据一致性
    $DB->query('BEGIN');

    try {
        // 使用FOR UPDATE锁定订单记录，防止并发问题
        $sql = "SELECT * FROM qingka_wangke_pay WHERE out_trade_no=? LIMIT 1 FOR UPDATE";
        $srow = $DB->prepare_getrow($sql, [$out_trade_no]);

        if (!$srow) {
            $DB->query('ROLLBACK');
            echo "fail"; // 订单不存在
            exit;
        }

        // 检查支付状态和金额
        if ($_GET['trade_status'] == 'TRADE_SUCCESS' && $srow['status'] == 0 && $srow['money'] == $money) {

            // 锁定用户记录
            $sql = "SELECT * FROM qingka_wangke_user WHERE uid=? FOR UPDATE";
            $userrow = $DB->prepare_getrow($sql, [$srow['uid']]);

            if (!$userrow) {
                $DB->query('ROLLBACK');
                echo "fail"; // 用户不存在
                exit;
            }

            // 计算赠送金额（根据原有逻辑）
            $money3 = 0;
            /*
            if ($money >= 50 && $money < 100) {
                $money3 = $money * 0.02;
            } elseif ($money >= 100 && $money < 300) {
                $money3 = $money * 0.05;
            } elseif ($money >= 300 && $money < 500) {
                $money3 = $money * 0.08;
            } elseif ($money >= 500) {
                $money3 = $money * 0.10;
            }
            */

            // 计算最终金额
            $money1 = $userrow['money'];
            $money2 = $money1 + $money + $money3;

            // 更新订单状态
            $sql = "UPDATE qingka_wangke_pay SET status='1', endtime=?, trade_no=? WHERE out_trade_no=?";
            $DB->prepare_query($sql, [$date, $trade_no, $out_trade_no]);

            // 更新用户余额和总充值
            $sql = "UPDATE qingka_wangke_user SET money=?, zcz=zcz+? WHERE uid=?";
            $DB->prepare_query($sql, [$money2, $money, $userrow['uid']]);

            // 记录充值日志
            wlog($userrow['uid'], "在线充值", "用户[{$userrow['user']}]在线充值了{$money}积分", $money);

            // 记录赠送日志（如果有赠送）
            if ($money3 > 0) {
                wlog($userrow['uid'], "在线充值", "用户[{$userrow['user']}]充值金额达标赠送{$money3}积分", $money3);
            }

        // 记录安全日志
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $request_data = json_encode($_GET);
        $sql = "INSERT INTO qingka_wangke_security_log (event_type, ip, user_agent, request_data, result, details, created_at) VALUES (?,?,?,?,?,?,?)";
        $DB->prepare_query($sql, ['payment_notify', $clientip, $user_agent, $request_data, 'success', "Payment processed for order {$out_trade_no}", date('Y-m-d H:i:s')]);

            $DB->query('COMMIT');
            echo "success";

        } elseif ($_GET['trade_status'] == 'TRADE_SUCCESS' && $srow['status'] == 1) {
            // 订单已处理，避免重复处理
            $DB->query('ROLLBACK');
            echo "success"; // 返回success避免支付平台重复通知

        } else {
            // 其他情况，只更新订单时间
            $sql = "UPDATE qingka_wangke_pay SET endtime=?, trade_no=? WHERE out_trade_no=?";
            $DB->prepare_query($sql, [$date, $trade_no, $out_trade_no]);
            $DB->query('COMMIT');
            echo "success";
        }

    } catch (Exception $e) {
        $DB->query('ROLLBACK');
        // 记录错误日志
        error_log("充值处理异常: " . $e->getMessage() . " - 订单号: " . $out_trade_no);
        echo "fail";
    }

} else {
    echo "fail";
}
?>