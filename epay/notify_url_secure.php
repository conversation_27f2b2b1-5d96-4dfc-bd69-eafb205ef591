<?php
/**
 * 安全加固版本的支付回调处理
 * 修复了多个安全漏洞，提供更强的安全保障
 */

@header('Content-Type: text/html; charset=UTF-8');
include("../confing/common.php");
require_once("epay/notify.class.php");

// 记录安全日志函数
function logSecurityEvent($event_type, $result, $details = '') {
    global $DB, $clientip;
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $request_data = json_encode($_GET);
    
    $sql = "INSERT INTO qingka_wangke_security_log (event_type, ip, user_agent, request_data, result, details, created_at) VALUES (?,?,?,?,?,?,?)";
    $DB->prepare_query($sql, [$event_type, $clientip, $user_agent, $request_data, $result, $details, date('Y-m-d H:i:s')]);
}

// 验证金额函数
function validateAmount($money) {
    // 1. 基本验证
    if(!is_numeric($money) || $money <= 0) {
        return false;
    }
    
    // 2. 范围验证
    $min_amount = 1;    // 最小充值金额
    $max_amount = 10000; // 最大充值金额
    if($money < $min_amount || $money > $max_amount) {
        return false;
    }
    
    // 3. 精度验证（最多2位小数）
    if(round($money, 2) != $money) {
        return false;
    }
    
    return true;
}

// 防重放攻击检查
function checkReplay($out_trade_no, $timestamp = null, $nonce = null) {
    global $DB;
    
    // 1. 检查时间戳（如果提供）
    if($timestamp && abs(time() - $timestamp) > 300) {
        return false;
    }
    
    // 2. 检查nonce（如果提供）
    if($nonce) {
        $sql = "SELECT COUNT(*) FROM qingka_wangke_pay_nonce WHERE nonce=? AND created_at > ?";
        $count = $DB->prepare_count($sql, [$nonce, date('Y-m-d H:i:s', time()-300)]);
        if($count > 0) {
            return false; // nonce已使用
        }
        
        // 记录nonce
        $sql = "INSERT INTO qingka_wangke_pay_nonce (nonce, out_trade_no, created_at) VALUES (?,?,?)";
        $DB->prepare_query($sql, [$nonce, $out_trade_no, date('Y-m-d H:i:s')]);
    }
    
    return true;
}

// 频率限制检查
function checkRateLimit($ip, $uid = null) {
    global $DB;
    
    // IP频率限制：每分钟最多5次
    $sql = "SELECT COUNT(*) FROM qingka_wangke_security_log WHERE ip=? AND event_type='payment_notify' AND created_at > ?";
    $ip_count = $DB->prepare_count($sql, [$ip, date('Y-m-d H:i:s', time()-60)]);
    if($ip_count > 5) {
        return false;
    }
    
    // 用户频率限制：每小时最多10次（如果有用户ID）
    if($uid) {
        $sql = "SELECT COUNT(*) FROM qingka_wangke_log WHERE uid=? AND type='在线充值' AND addtime > ?";
        $user_count = $DB->prepare_count($sql, [$uid, date('Y-m-d H:i:s', time()-3600)]);
        if($user_count > 10) {
            return false;
        }
    }
    
    return true;
}

// 完整的输入验证
function validateInput($params) {
    $errors = [];
    
    // 必要参数检查
    $required_params = ['out_trade_no', 'trade_status', 'money', 'sign'];
    foreach($required_params as $param) {
        if(!isset($params[$param]) || empty($params[$param])) {
            $errors[] = "缺少必要参数: {$param}";
        }
    }
    
    if(!empty($errors)) {
        return $errors;
    }
    
    // 订单号验证
    if(!preg_match('/^[0-9]{14,20}$/', $params['out_trade_no'])) {
        $errors[] = "订单号格式错误";
    }
    
    // 金额验证
    if(!validateAmount($params['money'])) {
        $errors[] = "金额验证失败";
    }
    
    // 状态验证
    $valid_status = ['TRADE_SUCCESS', 'TRADE_FINISHED'];
    if(!in_array($params['trade_status'], $valid_status)) {
        $errors[] = "交易状态无效";
    }
    
    return empty($errors) ? true : $errors;
}

// 主处理逻辑
try {
    // 记录访问日志
    logSecurityEvent('payment_notify', 'start', 'Payment notification received');
    
    // 1. 频率限制检查
    if(!checkRateLimit($clientip)) {
        logSecurityEvent('payment_notify', 'blocked', 'Rate limit exceeded');
        echo "fail";
        exit;
    }
    
    // 2. 输入验证
    $validation_result = validateInput($_GET);
    if($validation_result !== true) {
        logSecurityEvent('payment_notify', 'invalid_input', implode(', ', $validation_result));
        echo "fail";
        exit;
    }
    
    // 3. 支付验签
    $alipayNotify = new AlipayNotify($alipay_config);
    $verify_result = $alipayNotify->verifyNotify();
    
    if(!$verify_result) {
        logSecurityEvent('payment_notify', 'verify_failed', 'Payment verification failed');
        echo "fail";
        exit;
    }
    
    // 4. 提取和清理参数
    $out_trade_no = preg_replace('/[^0-9]/', '', $_GET['out_trade_no']);
    $trade_no = trim($_GET['trade_no']);
    $trade_status = trim($_GET['trade_status']);
    $money = round(floatval($_GET['money']), 2);
    $timestamp = isset($_GET['timestamp']) ? intval($_GET['timestamp']) : null;
    $nonce = isset($_GET['nonce']) ? trim($_GET['nonce']) : null;
    
    // 5. 防重放攻击检查
    if(!checkReplay($out_trade_no, $timestamp, $nonce)) {
        logSecurityEvent('payment_notify', 'replay_attack', "Replay attack detected for order: {$out_trade_no}");
        echo "success"; // 返回success避免支付平台重复通知
        exit;
    }
    
    // 6. 开始事务处理
    $DB->query('BEGIN');
    
    // 7. 锁定订单记录
    $sql = "SELECT * FROM qingka_wangke_pay WHERE out_trade_no=? LIMIT 1 FOR UPDATE";
    $srow = $DB->prepare_getrow($sql, [$out_trade_no]);
    
    if (!$srow) {
        $DB->query('ROLLBACK');
        logSecurityEvent('payment_notify', 'order_not_found', "Order not found: {$out_trade_no}");
        echo "fail";
        exit;
    }
    
    // 8. 验证订单金额
    if($srow['money'] != $money) {
        $DB->query('ROLLBACK');
        logSecurityEvent('payment_notify', 'amount_mismatch', "Amount mismatch for order {$out_trade_no}: expected {$srow['money']}, got {$money}");
        echo "fail";
        exit;
    }
    
    // 9. 检查支付状态和订单状态
    if ($trade_status == 'TRADE_SUCCESS' && $srow['status'] == 0) {
        
        // 10. 锁定用户记录
        $sql = "SELECT * FROM qingka_wangke_user WHERE uid=? FOR UPDATE";
        $userrow = $DB->prepare_getrow($sql, [$srow['uid']]);
        
        if (!$userrow) {
            $DB->query('ROLLBACK');
            logSecurityEvent('payment_notify', 'user_not_found', "User not found for order: {$out_trade_no}");
            echo "fail";
            exit;
        }
        
        // 11. 计算赠送金额（根据原有逻辑）
        $money3 = 0;
        /*
        if ($money >= 50 && $money < 100) {
            $money3 = $money * 0.02;
        } elseif ($money >= 100 && $money < 300) {
            $money3 = $money * 0.05;
        } elseif ($money >= 300 && $money < 500) {
            $money3 = $money * 0.08;
        } elseif ($money >= 500) {
            $money3 = $money * 0.10;
        }
        */
        
        // 12. 计算最终金额
        $money1 = $userrow['money'];
        $money2 = $money1 + $money + $money3;
        
        // 13. 更新订单状态
        $sql = "UPDATE qingka_wangke_pay SET status='1', endtime=?, trade_no=? WHERE out_trade_no=?";
        $DB->prepare_query($sql, [$date, $trade_no, $out_trade_no]);
        
        // 14. 更新用户余额和总充值
        $sql = "UPDATE qingka_wangke_user SET money=?, zcz=zcz+? WHERE uid=?";
        $DB->prepare_query($sql, [$money2, $money, $userrow['uid']]);
        
        // 15. 记录充值日志
        wlog($userrow['uid'], "在线充值", "用户[{$userrow['user']}]在线充值了{$money}积分", $money);
        
        // 16. 记录赠送日志（如果有赠送）
        if ($money3 > 0) {
            wlog($userrow['uid'], "在线充值", "用户[{$userrow['user']}]充值金额达标赠送{$money3}积分", $money3);
        }
        
        // 17. 提交事务
        $DB->query('COMMIT');
        
        // 18. 记录成功日志
        logSecurityEvent('payment_notify', 'success', "Payment processed successfully for order {$out_trade_no}, amount: {$money}");
        
        echo "success";
        
    } elseif ($trade_status == 'TRADE_SUCCESS' && $srow['status'] == 1) {
        // 订单已处理，避免重复处理
        $DB->query('ROLLBACK');
        logSecurityEvent('payment_notify', 'already_processed', "Order already processed: {$out_trade_no}");
        echo "success"; // 返回success避免支付平台重复通知
        
    } else {
        // 其他情况，只更新订单时间
        $sql = "UPDATE qingka_wangke_pay SET endtime=?, trade_no=? WHERE out_trade_no=?";
        $DB->prepare_query($sql, [$date, $trade_no, $out_trade_no]);
        $DB->query('COMMIT');
        
        logSecurityEvent('payment_notify', 'other_status', "Other status for order {$out_trade_no}: {$trade_status}, order status: {$srow['status']}");
        echo "success";
    }
    
} catch (Exception $e) {
    if($DB) {
        $DB->query('ROLLBACK');
    }
    
    // 记录错误日志
    $error_msg = "Payment processing exception: " . $e->getMessage();
    error_log($error_msg . " - Order: " . ($out_trade_no ?? 'unknown'));
    logSecurityEvent('payment_notify', 'exception', $error_msg);
    
    echo "fail";
}
?>
