/**
 * 前端价格格式化工具
 * 解决JavaScript浮点数精度问题
 * 作者：系统优化团队
 * 创建时间：2025-08-26
 */

/**
 * 格式化价格，解决浮点数精度问题
 * @param {number} price 原始价格
 * @param {number} decimals 小数位数，默认2位
 * @returns {number} 格式化后的价格
 */
function formatPrice(price, decimals = 2) {
    if (isNaN(price) || price === null || price === undefined) {
        return 0;
    }
    return Math.round(parseFloat(price) * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

/**
 * 计算用户价格
 * @param {number} basePrice 基础价格
 * @param {number} userRate 用户费率
 * @param {string} operation 运算方式 (*|+)
 * @returns {number} 计算后的价格
 */
function calculateUserPrice(basePrice, userRate, operation = '*') {
    if (operation === '*') {
        return formatPrice(basePrice * userRate);
    } else if (operation === '+') {
        return formatPrice(basePrice + userRate);
    } else {
        return formatPrice(basePrice * userRate);
    }
}

/**
 * 格式化价格显示（带货币符号）
 * @param {number} price 价格
 * @param {string} currency 货币符号，默认为空
 * @returns {string} 格式化后的价格字符串
 */
function formatPriceDisplay(price, currency = '') {
    const formattedPrice = formatPrice(price);
    return currency + formattedPrice.toString();
}

/**
 * 批量更新页面中的价格显示
 * @param {string} selector CSS选择器
 * @param {number} rate 费率
 */
function updatePricesInPage(selector, rate) {
    document.querySelectorAll(selector).forEach(function(element) {
        const basePrice = parseFloat(element.getAttribute('data-base-price') || 0);
        const newPrice = formatPrice(basePrice * rate);
        element.textContent = newPrice;
    });
}

/**
 * Vue.js 价格格式化过滤器
 */
if (typeof Vue !== 'undefined') {
    Vue.filter('formatPrice', function(value, decimals = 2) {
        return formatPrice(value, decimals);
    });
    
    Vue.filter('formatPriceDisplay', function(value, currency = '') {
        return formatPriceDisplay(value, currency);
    });
}

// 导出函数（如果支持模块化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        formatPrice,
        calculateUserPrice,
        formatPriceDisplay,
        updatePricesInPage
    };
}
