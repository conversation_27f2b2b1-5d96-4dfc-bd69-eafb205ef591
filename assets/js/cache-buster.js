/**
 * 前端缓存清理和强制刷新机制
 * 解决商品搜索缓存问题
 */

// 缓存清理管理器
const CacheBuster = {
    // 检查是否需要强制刷新
    checkForceRefresh() {
        const lastCacheVersion = localStorage.getItem('cache_version');
        const currentTime = Date.now();
        
        // 检查服务器缓存版本
        fetch('/apisub.php?act=get_cache_version', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 1) {
                const serverCacheVersion = data.cache_version;
                
                if (lastCacheVersion !== serverCacheVersion) {
                    console.log('检测到服务器缓存更新，清理本地缓存');
                    this.clearAllCache();
                    localStorage.setItem('cache_version', serverCacheVersion);
                    
                    // 显示提示
                    if (typeof layer !== 'undefined') {
                        layer.msg('检测到数据更新，已自动刷新', {icon: 1, time: 2000});
                    }
                }
            }
        })
        .catch(error => {
            console.log('缓存版本检查失败:', error);
        });
    },
    
    // 清理所有本地缓存
    clearAllCache() {
        // 清理localStorage中的缓存
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
            if (key.startsWith('category_') || 
                key.startsWith('class_list_') || 
                key.startsWith('search_')) {
                localStorage.removeItem(key);
            }
        });
        
        // 清理sessionStorage中的缓存
        const sessionKeys = Object.keys(sessionStorage);
        sessionKeys.forEach(key => {
            if (key.startsWith('category_') || 
                key.startsWith('class_list_') || 
                key.startsWith('search_')) {
                sessionStorage.removeItem(key);
            }
        });
        
        // 清理Vue组件中的缓存（如果存在）
        if (typeof window.vm !== 'undefined' && window.vm) {
            if (window.vm.class1) {
                window.vm.class1 = [];
            }
            if (window.vm.filteredClass1) {
                window.vm.filteredClass1 = [];
            }
        }
        
        console.log('本地缓存已清理');
    },
    
    // 强制刷新分类数据
    forceRefreshCategory(categoryId) {
        if (typeof window.vm !== 'undefined' && window.vm && window.vm.fenlei) {
            // 添加强制刷新参数
            const originalFenlei = window.vm.fenlei;
            window.vm.fenlei = function(id) {
                const load = layer.load(2);
                return this.$http.post("/apisub.php?act=getclassfl", { 
                    id: id, 
                    force_refresh: true,
                    _t: Date.now() // 防止浏览器缓存
                }, { emulateJSON: true })
                .then(response => {
                    layer.close(load);
                    if (response.data.code === 1) {
                        this.class1 = response.body.data;
                        this.filteredClass1 = this.class1;
                        this.keyword = '';
                        console.log('分类数据已强制刷新:', response.body.data.length, '个商品');
                    } else {
                        layer.msg(response.data.msg, { icon: 2 });
                    }
                })
                .catch(error => {
                    layer.close(load);
                    console.error('强制刷新失败:', error);
                    layer.msg('刷新失败，请重试', { icon: 2 });
                });
            };
            
            // 立即执行强制刷新
            window.vm.fenlei(categoryId);
        }
    },
    
    // 添加强制刷新按钮
    addRefreshButton() {
        // 检查是否已经添加了按钮
        if (document.getElementById('force-refresh-btn')) {
            return;
        }
        
        // 创建强制刷新按钮
        const refreshBtn = document.createElement('button');
        refreshBtn.id = 'force-refresh-btn';
        refreshBtn.className = 'layui-btn layui-btn-sm layui-btn-normal';
        refreshBtn.innerHTML = '<i class="layui-icon layui-icon-refresh"></i> 强制刷新';
        refreshBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999;';
        
        refreshBtn.onclick = () => {
            if (typeof layer !== 'undefined') {
                layer.confirm('确定要强制刷新所有数据吗？', {icon: 3, title:'提示'}, (index) => {
                    this.clearAllCache();
                    location.reload(true); // 强制刷新页面
                    layer.close(index);
                });
            } else {
                if (confirm('确定要强制刷新所有数据吗？')) {
                    this.clearAllCache();
                    location.reload(true);
                }
            }
        };
        
        document.body.appendChild(refreshBtn);
    },
    
    // 初始化
    init() {
        // 页面加载时检查缓存版本
        this.checkForceRefresh();
        
        // 添加强制刷新按钮（仅在开发环境或管理员模式下）
        if (window.location.hostname === 'localhost' || 
            window.location.search.includes('debug=1')) {
            this.addRefreshButton();
        }
        
        // 定期检查缓存版本（每5分钟）
        setInterval(() => {
            this.checkForceRefresh();
        }, 5 * 60 * 1000);
    }
};

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        CacheBuster.init();
    });
} else {
    CacheBuster.init();
}

// 暴露到全局作用域，方便调试
window.CacheBuster = CacheBuster;
