<?php
/**
 * 订单通知配置文件
 * 用于配置通知合并规则和行为
 */

// 通知合并配置
$notification_config = [
    // 合并规则
    'merge_rules' => [
        'single_order' => 1,        // 1个订单：发送详细通知
        'multiple_threshold' => 3,   // 2-3个订单：逐条发送
        'batch_threshold' => 4,      // 4个以上：合并发送
    ],
    
    // 通知间隔（微秒）
    'notification_intervals' => [
        'between_single' => 500000,  // 单条通知间隔0.5秒
        'between_batch' => 1000000,  // 批量通知间隔1秒
    ],
    
    // 批量通知显示规则
    'batch_display' => [
        'max_detail_orders' => 5,    // 最多显示5个订单详情
        'max_total_orders' => 20,    // 单次批量最多20个订单
    ],
    
    // 防重复配置
    'duplicate_prevention' => [
        'cache_duration' => 86400,   // 24小时缓存
        'redis_key_prefix' => 'order_notify_',
    ],
    
    // 性能限制
    'performance_limits' => [
        'max_orders_per_run' => 50,  // 单次最多处理50个订单
        'max_notifications_per_user' => 10, // 单用户最多10条通知
    ]
];

/**
 * 获取通知配置
 */
function getNotificationConfig($key = null, $default = null) {
    global $notification_config;
    
    if ($key === null) {
        return $notification_config;
    }
    
    $keys = explode('.', $key);
    $value = $notification_config;
    
    foreach ($keys as $k) {
        if (isset($value[$k])) {
            $value = $value[$k];
        } else {
            return $default;
        }
    }
    
    return $value;
}

/**
 * 判断是否应该合并通知
 */
function shouldMergeNotifications($order_count) {
    $batch_threshold = getNotificationConfig('merge_rules.batch_threshold', 6);
    return $order_count >= $batch_threshold;
}

/**
 * 判断是否应该逐条发送
 */
function shouldSendIndividually($order_count) {
    $multiple_threshold = getNotificationConfig('merge_rules.multiple_threshold', 5);
    return $order_count <= $multiple_threshold;
}

/**
 * 获取通知间隔时间
 */
function getNotificationInterval($type = 'single') {
    $key = "notification_intervals.between_{$type}";
    return getNotificationConfig($key, 500000);
}

/**
 * 获取批量显示配置
 */
function getBatchDisplayConfig() {
    return getNotificationConfig('batch_display', [
        'max_detail_orders' => 3,
        'max_total_orders' => 20
    ]);
}

/**
 * 生成Redis缓存键
 */
function generateNotificationKey($oid, $status, $date = null) {
    $prefix = getNotificationConfig('duplicate_prevention.redis_key_prefix', 'order_notify_');
    $date = $date ?: date('Y-m-d');
    return "{$prefix}{$oid}_{$status}_{$date}";
}

/**
 * 获取缓存持续时间
 */
function getCacheDuration() {
    return getNotificationConfig('duplicate_prevention.cache_duration', 86400);
}

/**
 * 检查性能限制
 */
function checkPerformanceLimits($current_count, $type = 'orders') {
    $limits = getNotificationConfig('performance_limits', []);
    
    switch ($type) {
        case 'orders':
            $max = $limits['max_orders_per_run'] ?? 50;
            break;
        case 'notifications':
            $max = $limits['max_notifications_per_user'] ?? 10;
            break;
        default:
            return true;
    }
    
    return $current_count < $max;
}

/**
 * 记录通知统计
 */
function recordNotificationStats($type, $count, $user_id = null) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] 通知统计: {$type} - {$count}个";
    if ($user_id) {
        $log_entry .= " (用户: {$user_id})";
    }
    
    // 这里可以扩展为写入数据库或其他统计系统
    error_log($log_entry);
}

/**
 * 验证通知配置
 */
function validateNotificationConfig() {
    $errors = [];
    
    // 检查必要的配置项
    $required_configs = [
        'merge_rules.single_order',
        'merge_rules.multiple_threshold', 
        'merge_rules.batch_threshold',
        'performance_limits.max_orders_per_run'
    ];
    
    foreach ($required_configs as $config) {
        if (getNotificationConfig($config) === null) {
            $errors[] = "缺少必要配置: {$config}";
        }
    }
    
    // 检查逻辑合理性
    $single = getNotificationConfig('merge_rules.single_order', 1);
    $multiple = getNotificationConfig('merge_rules.multiple_threshold', 5);
    $batch = getNotificationConfig('merge_rules.batch_threshold', 6);
    
    if ($multiple >= $batch) {
        $errors[] = "multiple_threshold 应该小于 batch_threshold";
    }
    
    if ($single > $multiple) {
        $errors[] = "single_order 应该小于等于 multiple_threshold";
    }
    
    return empty($errors) ? true : $errors;
}

// 在脚本启动时验证配置
$config_validation = validateNotificationConfig();
if ($config_validation !== true) {
    error_log("通知配置验证失败: " . implode(', ', $config_validation));
}

?>
