<?php
/**
 * 客户查单页面
 * 独立的查单功能，客户可以通过账号查询自己的订单信息
 * 支持单个账号查询和批量账号查询
 */

// 引入数据库配置
include('confing/mysqlset.php');

// 创建数据库连接
$mysqli = new mysqli($host, $user, $pwd, $dbname, $port);
if ($mysqli->connect_error) {
    die("数据库连接失败: " . $mysqli->connect_error);
}
$mysqli->set_charset("utf8");

// 处理查询请求
$orders = [];
$search_user = '';
$batch_users = '';
$error_msg = '';
$success_msg = '';
$is_batch_search = false;

if ($_POST) {
    // 检查是单个查询还是批量查询
    if (isset($_POST['search_user']) && !empty(trim($_POST['search_user']))) {
        // 单个账号查询
        $search_user = trim($_POST['search_user']);
        $users_to_search = [$search_user];
        $is_batch_search = false;
    } elseif (isset($_POST['batch_users']) && !empty(trim($_POST['batch_users']))) {
        // 批量账号查询
        $batch_users = trim($_POST['batch_users']);
        $is_batch_search = true;

        // 解析批量账号（支持换行、逗号、分号分隔）
        $users_to_search = preg_split('/[\r\n,;，；]+/', $batch_users);
        $users_to_search = array_filter(array_map('trim', $users_to_search));

        if (count($users_to_search) > 50) {
            $error_msg = "批量查询最多支持50个账号，您输入了" . count($users_to_search) . "个账号";
            $users_to_search = [];
        }
    } else {
        $error_msg = "请输入要查询的账号";
        $users_to_search = [];
    }

    // 执行查询
    if (!empty($users_to_search) && empty($error_msg)) {
        $placeholders = str_repeat('?,', count($users_to_search) - 1) . '?';
        $sql = "SELECT oid, ptname, school, name, user, kcname, status, process, remarks, addtime,
                       courseStartTime, courseEndTime, examStartTime, examEndTime
                FROM qingka_wangke_order
                WHERE user IN ($placeholders)
                ORDER BY user, oid DESC
                LIMIT 500";

        $stmt = $mysqli->prepare($sql);
        if ($stmt) {
            $types = str_repeat('s', count($users_to_search));
            $stmt->bind_param($types, ...$users_to_search);
            $stmt->execute();
            $result = $stmt->get_result();

            while ($row = $result->fetch_assoc()) {
                $orders[] = $row;
            }
            $stmt->close();

            if (empty($orders)) {
                $error_msg = $is_batch_search ? "未找到任何账号的订单信息" : "未找到该账号的订单信息";
            } else {
                $found_users = array_unique(array_column($orders, 'user'));
                $total_orders = count($orders);
                $total_users = count($found_users);

                if ($is_batch_search) {
                    $not_found_users = array_diff($users_to_search, $found_users);
                    $success_msg = "找到 {$total_users} 个账号的 {$total_orders} 条订单记录";
                    if (!empty($not_found_users)) {
                        $success_msg .= "，未找到订单的账号：" . implode('、', array_slice($not_found_users, 0, 5));
                        if (count($not_found_users) > 5) {
                            $success_msg .= " 等" . count($not_found_users) . "个账号";
                        }
                    }
                } else {
                    $success_msg = "找到 {$total_orders} 条订单记录";
                }
            }
        } else {
            $error_msg = "查询失败，请稍后重试";
        }
    }
}

$mysqli->close();

// 状态颜色映射
function getStatusColor($status) {
    $colors = [
        '待处理' => '#f39c12',
        '待上号' => '#f39c12', 
        '排队中' => '#f39c12',
        '已暂停' => '#f39c12',
        '已停止' => '#e74c3c',
        '已完成' => '#27ae60',
        '待更新' => '#3498db',
        '异常' => '#e74c3c',
        '失败' => '#e74c3c',
        '密码错误' => '#e74c3c',
        '已提取' => '#3498db',
        '已提交' => '#3498db',
        '进行中' => '#3498db',
        '上号中' => '#3498db',
        '考试中' => '#3498db',
        '队列中' => '#f39c12',
        '待考试' => '#3498db',
        '待重启' => '#3498db',
        '等待下周' => '#3498db',
        '请自行处理' => '#27ae60',
        '平时分' => '#3498db',
        '平时分中' => '#3498db',
        '补刷中' => '#9b59b6'
    ];
    return isset($colors[$status]) ? $colors[$status] : '#95a5a6';
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单查询系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .search-section {
            padding: 40px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .search-form {
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .search-input {
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            width: 300px;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .search-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .search-mode-toggle {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 10px;
        }

        .mode-btn {
            padding: 10px 20px;
            border: 2px solid #e9ecef;
            background: white;
            color: #6c757d;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .mode-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .mode-btn:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .mode-btn.active:hover {
            color: white;
        }

        .batch-form {
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .batch-input-container {
            display: flex;
            gap: 20px;
            width: 100%;
            max-width: 800px;
            align-items: flex-start;
        }

        .batch-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 14px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            resize: vertical;
            min-height: 150px;
            transition: all 0.3s ease;
        }

        .batch-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .batch-tips {
            flex: 0 0 200px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            font-size: 13px;
            color: #6c757d;
        }

        .batch-tips p {
            margin-bottom: 10px;
            font-weight: 600;
            color: #495057;
        }

        .batch-tips ul {
            margin: 0;
            padding-left: 15px;
        }

        .batch-tips li {
            margin-bottom: 5px;
        }

        .batch-btn {
            width: auto;
            min-width: 150px;
        }
        
        .message {
            margin: 20px 40px;
            padding: 15px 20px;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
        
        .success {
            background: #efe;
            color: #363;
            border: 1px solid #cfc;
        }
        
        .results-section {
            padding: 40px;
        }
        
        .order-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .order-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .order-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .order-id {
            font-size: 1.2em;
            font-weight: 600;
            color: #495057;
        }
        
        .order-status {
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            font-weight: 500;
            font-size: 0.9em;
        }
        
        .order-body {
            padding: 25px;
        }
        
        .order-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-weight: 600;
            color: #6c757d;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #495057;
            font-size: 1em;
            word-break: break-all;
        }
        
        .progress-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            height: 100%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .no-results i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .user-group {
            margin-bottom: 40px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            background: white;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .user-group-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .user-group-header h3 {
            margin: 0;
            font-size: 1.3em;
            font-weight: 500;
        }

        .order-count {
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .user-group .order-card {
            margin: 20px;
            margin-bottom: 20px;
        }

        .user-group .order-card:last-child {
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }

            .search-input {
                width: 100%;
            }

            .batch-input-container {
                flex-direction: column;
            }

            .batch-tips {
                flex: none;
            }

            .order-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .order-info {
                grid-template-columns: 1fr;
            }

            .user-group-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 订单查询系统</h1>
            <p>支持单个账号查询和批量账号查询</p>
        </div>

        <div class="search-section">
            <!-- 查询模式切换 -->
            <div class="search-mode-toggle">
                <button type="button" class="mode-btn active" onclick="switchMode('single')">单个查询</button>
                <button type="button" class="mode-btn" onclick="switchMode('batch')">批量查询</button>
            </div>

            <!-- 单个查询表单 -->
            <form method="POST" class="search-form" id="single-form">
                <input type="text"
                       name="search_user"
                       class="search-input"
                       placeholder="请输入您的学习账号"
                       value="<?php echo htmlspecialchars($search_user); ?>">
                <button type="submit" class="search-btn">🔍 查询订单</button>
            </form>

            <!-- 批量查询表单 -->
            <form method="POST" class="search-form batch-form" id="batch-form" style="display: none;">
                <div class="batch-input-container">
                    <textarea name="batch_users"
                              class="batch-input"
                              placeholder="请输入多个学习账号，每行一个或用逗号分隔&#10;例如：&#10;account1&#10;account2&#10;account3"
                              rows="6"><?php echo htmlspecialchars($batch_users); ?></textarea>
                    <div class="batch-tips">
                        <p>💡 支持格式：</p>
                        <ul>
                            <li>每行一个账号</li>
                            <li>逗号分隔：account1,account2,account3</li>
                            <li>分号分隔：account1;account2;account3</li>
                            <li>最多支持50个账号</li>
                        </ul>
                    </div>
                </div>
                <button type="submit" class="search-btn batch-btn">🔍 批量查询</button>
            </form>
        </div>
        
        <?php if ($error_msg): ?>
            <div class="message error"><?php echo htmlspecialchars($error_msg); ?></div>
        <?php endif; ?>
        
        <?php if ($success_msg): ?>
            <div class="message success"><?php echo htmlspecialchars($success_msg); ?></div>
        <?php endif; ?>
        
        <div class="results-section">
            <?php if (!empty($orders)): ?>
                <?php if ($is_batch_search): ?>
                    <!-- 批量查询结果按用户分组显示 -->
                    <?php
                    $grouped_orders = [];
                    foreach ($orders as $order) {
                        $grouped_orders[$order['user']][] = $order;
                    }
                    ?>
                    <?php foreach ($grouped_orders as $username => $user_orders): ?>
                        <div class="user-group">
                            <div class="user-group-header">
                                <h3>👤 账号：<?php echo htmlspecialchars($username); ?></h3>
                                <span class="order-count"><?php echo count($user_orders); ?> 条订单</span>
                            </div>
                            <?php foreach ($user_orders as $order): ?>
                                <div class="order-card">
                                    <div class="order-header">
                                        <div class="order-id">订单 #<?php echo $order['oid']; ?></div>
                                        <div class="order-status" style="background-color: <?php echo getStatusColor($order['status']); ?>">
                                            <?php echo htmlspecialchars($order['status']); ?>
                                        </div>
                                    </div>

                                    <div class="order-body">
                                        <div class="order-info">
                                            <div class="info-item">
                                                <div class="info-label">🏫 学校</div>
                                                <div class="info-value"><?php echo htmlspecialchars($order['school'] ?: '未填写'); ?></div>
                                            </div>

                                            <div class="info-item">
                                                <div class="info-label">👤 姓名</div>
                                                <div class="info-value"><?php echo htmlspecialchars($order['name'] ?: '未填写'); ?></div>
                                            </div>

                                            <div class="info-item">
                                                <div class="info-label">📚 课程名称</div>
                                                <div class="info-value"><?php echo htmlspecialchars($order['kcname']); ?></div>
                                            </div>

                                            <div class="info-item">
                                                <div class="info-label">🌐 平台</div>
                                                <div class="info-value"><?php echo htmlspecialchars($order['ptname']); ?></div>
                                            </div>

                                            <div class="info-item">
                                                <div class="info-label">📅 下单时间</div>
                                                <div class="info-value"><?php echo $order['addtime']; ?></div>
                                            </div>

                                            <?php if ($order['courseStartTime']): ?>
                                            <div class="info-item">
                                                <div class="info-label">🕐 课程开始时间</div>
                                                <div class="info-value"><?php echo $order['courseStartTime']; ?></div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if ($order['courseEndTime']): ?>
                                            <div class="info-item">
                                                <div class="info-label">🕕 课程结束时间</div>
                                                <div class="info-value"><?php echo $order['courseEndTime']; ?></div>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($order['remarks']): ?>
                                        <div class="info-item">
                                            <div class="info-label">📝 备注信息</div>
                                            <div class="info-value"><?php echo htmlspecialchars($order['remarks']); ?></div>
                                        </div>
                                        <?php endif; ?>

                                        <div class="progress-section">
                                            <div class="info-label">📊 完成进度</div>
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: <?php echo $order['process']; ?>">
                                                    <?php echo $order['process']; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- 单个查询结果 -->
                    <?php foreach ($orders as $order): ?>
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-id">订单 #<?php echo $order['oid']; ?></div>
                                <div class="order-status" style="background-color: <?php echo getStatusColor($order['status']); ?>">
                                    <?php echo htmlspecialchars($order['status']); ?>
                                </div>
                            </div>

                            <div class="order-body">
                                <div class="order-info">
                                    <div class="info-item">
                                        <div class="info-label">🏫 学校</div>
                                        <div class="info-value"><?php echo htmlspecialchars($order['school'] ?: '未填写'); ?></div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">👤 姓名</div>
                                        <div class="info-value"><?php echo htmlspecialchars($order['name'] ?: '未填写'); ?></div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">📚 课程名称</div>
                                        <div class="info-value"><?php echo htmlspecialchars($order['kcname']); ?></div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">🌐 平台</div>
                                        <div class="info-value"><?php echo htmlspecialchars($order['ptname']); ?></div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">📅 下单时间</div>
                                        <div class="info-value"><?php echo $order['addtime']; ?></div>
                                    </div>

                                    <?php if ($order['courseStartTime']): ?>
                                    <div class="info-item">
                                        <div class="info-label">🕐 课程开始时间</div>
                                        <div class="info-value"><?php echo $order['courseStartTime']; ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($order['courseEndTime']): ?>
                                    <div class="info-item">
                                        <div class="info-label">🕕 课程结束时间</div>
                                        <div class="info-value"><?php echo $order['courseEndTime']; ?></div>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <?php if ($order['remarks']): ?>
                                <div class="info-item">
                                    <div class="info-label">📝 备注信息</div>
                                    <div class="info-value"><?php echo htmlspecialchars($order['remarks']); ?></div>
                                </div>
                                <?php endif; ?>

                                <div class="progress-section">
                                    <div class="info-label">📊 完成进度</div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: <?php echo $order['process']; ?>">
                                            <?php echo $order['process']; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            <?php elseif ($_POST): ?>
                <div class="no-results">
                    <div style="font-size: 4em; margin-bottom: 20px; opacity: 0.5;">🔍</div>
                    <h3>未找到订单信息</h3>
                    <p>请检查账号是否正确，或联系客服咨询</p>
                </div>
            <?php else: ?>
                <div class="no-results">
                    <div style="font-size: 4em; margin-bottom: 20px; opacity: 0.5;">📋</div>
                    <h3>请输入账号查询订单</h3>
                    <p>在上方输入框中输入您的学习账号即可查询订单进度</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // 切换查询模式
        function switchMode(mode) {
            const singleForm = document.getElementById('single-form');
            const batchForm = document.getElementById('batch-form');
            const modeBtns = document.querySelectorAll('.mode-btn');

            // 移除所有按钮的active类
            modeBtns.forEach(btn => btn.classList.remove('active'));

            if (mode === 'single') {
                singleForm.style.display = 'flex';
                batchForm.style.display = 'none';
                document.querySelector('.mode-btn:first-child').classList.add('active');

                // 清空批量输入框
                document.querySelector('textarea[name="batch_users"]').value = '';
            } else {
                singleForm.style.display = 'none';
                batchForm.style.display = 'flex';
                document.querySelector('.mode-btn:last-child').classList.add('active');

                // 清空单个输入框
                document.querySelector('input[name="search_user"]').value = '';
            }
        }

        // 页面加载时根据当前状态设置模式
        document.addEventListener('DOMContentLoaded', function() {
            const batchUsers = document.querySelector('textarea[name="batch_users"]').value;
            const singleUser = document.querySelector('input[name="search_user"]').value;

            if (batchUsers.trim()) {
                switchMode('batch');
            } else {
                switchMode('single');
            }
        });

        // 表单验证
        document.getElementById('single-form').addEventListener('submit', function(e) {
            const input = this.querySelector('input[name="search_user"]');
            if (!input.value.trim()) {
                e.preventDefault();
                alert('请输入要查询的账号');
                input.focus();
            }
        });

        document.getElementById('batch-form').addEventListener('submit', function(e) {
            const textarea = this.querySelector('textarea[name="batch_users"]');
            if (!textarea.value.trim()) {
                e.preventDefault();
                alert('请输入要查询的账号');
                textarea.focus();
                return;
            }

            // 检查账号数量
            const users = textarea.value.split(/[\r\n,;，；]+/).filter(u => u.trim());
            if (users.length > 50) {
                e.preventDefault();
                alert('批量查询最多支持50个账号，您输入了' + users.length + '个账号');
                textarea.focus();
            }
        });
    </script>
</body>
</html>
