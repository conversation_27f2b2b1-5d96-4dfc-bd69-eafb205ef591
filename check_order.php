<?php
/**
 * 客户查单页面
 * 独立的查单功能，客户可以通过账号查询自己的订单信息
 */

// 引入数据库配置
include('confing/mysqlset.php');

// 创建数据库连接
$mysqli = new mysqli($host, $user, $pwd, $dbname, $port);
if ($mysqli->connect_error) {
    die("数据库连接失败: " . $mysqli->connect_error);
}
$mysqli->set_charset("utf8");

// 处理查询请求
$orders = [];
$search_user = '';
$error_msg = '';
$success_msg = '';

if ($_POST && isset($_POST['search_user'])) {
    $search_user = trim($_POST['search_user']);
    
    if (empty($search_user)) {
        $error_msg = "请输入要查询的账号";
    } else {
        // 查询订单信息
        $sql = "SELECT oid, ptname, school, name, user, kcname, status, process, remarks, addtime, 
                       courseStartTime, courseEndTime, examStartTime, examEndTime 
                FROM qingka_wangke_order 
                WHERE user = ? 
                ORDER BY oid DESC 
                LIMIT 50";
        
        $stmt = $mysqli->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("s", $search_user);
            $stmt->execute();
            $result = $stmt->get_result();
            
            while ($row = $result->fetch_assoc()) {
                $orders[] = $row;
            }
            $stmt->close();
            
            if (empty($orders)) {
                $error_msg = "未找到该账号的订单信息";
            } else {
                $success_msg = "找到 " . count($orders) . " 条订单记录";
            }
        } else {
            $error_msg = "查询失败，请稍后重试";
        }
    }
}

$mysqli->close();

// 状态颜色映射
function getStatusColor($status) {
    $colors = [
        '待处理' => '#f39c12',
        '待上号' => '#f39c12', 
        '排队中' => '#f39c12',
        '已暂停' => '#f39c12',
        '已停止' => '#e74c3c',
        '已完成' => '#27ae60',
        '待更新' => '#3498db',
        '异常' => '#e74c3c',
        '失败' => '#e74c3c',
        '密码错误' => '#e74c3c',
        '已提取' => '#3498db',
        '已提交' => '#3498db',
        '进行中' => '#3498db',
        '上号中' => '#3498db',
        '考试中' => '#3498db',
        '队列中' => '#f39c12',
        '待考试' => '#3498db',
        '待重启' => '#3498db',
        '等待下周' => '#3498db',
        '请自行处理' => '#27ae60',
        '平时分' => '#3498db',
        '平时分中' => '#3498db',
        '补刷中' => '#9b59b6'
    ];
    return isset($colors[$status]) ? $colors[$status] : '#95a5a6';
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单查询系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .search-section {
            padding: 40px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .search-form {
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .search-input {
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            width: 300px;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .search-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .message {
            margin: 20px 40px;
            padding: 15px 20px;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
        
        .success {
            background: #efe;
            color: #363;
            border: 1px solid #cfc;
        }
        
        .results-section {
            padding: 40px;
        }
        
        .order-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .order-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .order-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .order-id {
            font-size: 1.2em;
            font-weight: 600;
            color: #495057;
        }
        
        .order-status {
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            font-weight: 500;
            font-size: 0.9em;
        }
        
        .order-body {
            padding: 25px;
        }
        
        .order-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-weight: 600;
            color: #6c757d;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #495057;
            font-size: 1em;
            word-break: break-all;
        }
        
        .progress-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            height: 100%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .no-results i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }
            
            .search-input {
                width: 100%;
            }
            
            .order-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .order-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 订单查询系统</h1>
            <p>请输入您的学习账号查询订单进度</p>
        </div>
        
        <div class="search-section">
            <form method="POST" class="search-form">
                <input type="text" 
                       name="search_user" 
                       class="search-input" 
                       placeholder="请输入您的学习账号" 
                       value="<?php echo htmlspecialchars($search_user); ?>"
                       required>
                <button type="submit" class="search-btn">🔍 查询订单</button>
            </form>
        </div>
        
        <?php if ($error_msg): ?>
            <div class="message error"><?php echo htmlspecialchars($error_msg); ?></div>
        <?php endif; ?>
        
        <?php if ($success_msg): ?>
            <div class="message success"><?php echo htmlspecialchars($success_msg); ?></div>
        <?php endif; ?>
        
        <div class="results-section">
            <?php if (!empty($orders)): ?>
                <?php foreach ($orders as $order): ?>
                    <div class="order-card">
                        <div class="order-header">
                            <div class="order-id">订单 #<?php echo $order['oid']; ?></div>
                            <div class="order-status" style="background-color: <?php echo getStatusColor($order['status']); ?>">
                                <?php echo htmlspecialchars($order['status']); ?>
                            </div>
                        </div>
                        
                        <div class="order-body">
                            <div class="order-info">
                                <div class="info-item">
                                    <div class="info-label">🏫 学校</div>
                                    <div class="info-value"><?php echo htmlspecialchars($order['school'] ?: '未填写'); ?></div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">👤 姓名</div>
                                    <div class="info-value"><?php echo htmlspecialchars($order['name'] ?: '未填写'); ?></div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">📚 课程名称</div>
                                    <div class="info-value"><?php echo htmlspecialchars($order['kcname']); ?></div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">🌐 平台</div>
                                    <div class="info-value"><?php echo htmlspecialchars($order['ptname']); ?></div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">📅 下单时间</div>
                                    <div class="info-value"><?php echo $order['addtime']; ?></div>
                                </div>
                                
                                <?php if ($order['courseStartTime']): ?>
                                <div class="info-item">
                                    <div class="info-label">🕐 课程开始时间</div>
                                    <div class="info-value"><?php echo $order['courseStartTime']; ?></div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($order['courseEndTime']): ?>
                                <div class="info-item">
                                    <div class="info-label">🕕 课程结束时间</div>
                                    <div class="info-value"><?php echo $order['courseEndTime']; ?></div>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($order['remarks']): ?>
                            <div class="info-item">
                                <div class="info-label">📝 备注信息</div>
                                <div class="info-value"><?php echo htmlspecialchars($order['remarks']); ?></div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="progress-section">
                                <div class="info-label">📊 完成进度</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: <?php echo $order['process']; ?>">
                                        <?php echo $order['process']; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php elseif ($_POST): ?>
                <div class="no-results">
                    <div style="font-size: 4em; margin-bottom: 20px; opacity: 0.5;">🔍</div>
                    <h3>未找到订单信息</h3>
                    <p>请检查账号是否正确，或联系客服咨询</p>
                </div>
            <?php else: ?>
                <div class="no-results">
                    <div style="font-size: 4em; margin-bottom: 20px; opacity: 0.5;">📋</div>
                    <h3>请输入账号查询订单</h3>
                    <p>在上方输入框中输入您的学习账号即可查询订单进度</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
