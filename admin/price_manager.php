<?php
/**
 * 超级管理员价格管理工具
 * 用于查看和管理数据库原价与用户显示价格
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 检查超级管理员权限
if ($islogin != 1 || $userrow['uid'] != 1) {
    die('仅超级管理员可访问此页面');
}

// 处理费率修改请求
if ($_POST['action'] == 'update_rate' && isset($_POST['new_rate'])) {
    $new_rate = floatval($_POST['new_rate']);
    if ($new_rate > 0) {
        $sql = "UPDATE qingka_wangke_user SET addprice = ? WHERE uid = 1";
        $DB->prepare_query($sql, [$new_rate]);
        $userrow['addprice'] = $new_rate; // 更新当前会话中的费率
        $success_msg = "费率已更新为 {$new_rate}";
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>超级管理员价格管理</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .admin-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; margin-bottom: 30px; }
        .price-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 10px 0; }
        .db-price { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .display-price { background: #f3e5f5; border-left: 4px solid #9c27b0; }
        .rate-control { background: #fff3e0; border-left: 4px solid #ff9800; }
        .highlight { background-color: #ffeb3b; padding: 2px 4px; border-radius: 3px; }
        .success-msg { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .quick-rates { margin: 10px 0; }
        .quick-rates button { margin: 5px; }
        table { font-size: 14px; }
        .price-diff { font-weight: bold; }
        .price-diff.positive { color: #4caf50; }
        .price-diff.negative { color: #f44336; }
        .price-diff.zero { color: #666; }
    </style>
</head>
<body>
    <div class="admin-header">
        <div class="container">
            <h1>🔧 超级管理员价格管理工具</h1>
            <p>管理数据库原价与用户显示价格的关系</p>
        </div>
    </div>

    <div class="container">
        <?php if (isset($success_msg)): ?>
        <div class="success-msg">✅ <?php echo $success_msg; ?></div>
        <?php endif; ?>

        <!-- 费率控制面板 -->
        <div class="price-card rate-control">
            <h3>🎛️ 费率控制面板</h3>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>当前费率：</strong> <span class="highlight"><?php echo $userrow['addprice']; ?></span></p>
                    <p><strong>价格计算公式：</strong> 显示价格 = 数据库价格 × <?php echo $userrow['addprice']; ?></p>
                    
                    <form method="post" style="margin-top: 15px;">
                        <input type="hidden" name="action" value="update_rate">
                        <div class="input-group">
                            <input type="number" name="new_rate" step="0.01" min="0.01" max="10" 
                                   value="<?php echo $userrow['addprice']; ?>" class="form-control" 
                                   placeholder="输入新费率">
                            <span class="input-group-btn">
                                <button type="submit" class="btn btn-primary">更新费率</button>
                            </span>
                        </div>
                    </form>
                </div>
                <div class="col-md-6">
                    <p><strong>快速设置：</strong></p>
                    <div class="quick-rates">
                        <button onclick="setRate(0.2)" class="btn btn-sm btn-default">0.2 (最低)</button>
                        <button onclick="setRate(0.5)" class="btn btn-sm btn-default">0.5 (五折)</button>
                        <button onclick="setRate(1.0)" class="btn btn-sm btn-success">1.0 (原价)</button>
                        <button onclick="setRate(1.5)" class="btn btn-sm btn-default">1.5 (1.5倍)</button>
                        <button onclick="setRate(2.0)" class="btn btn-sm btn-default">2.0 (双倍)</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 价格对比说明 -->
        <div class="row">
            <div class="col-md-4">
                <div class="price-card db-price">
                    <h4>📊 数据库原价</h4>
                    <p>存储在 <code>qingka_wangke_class.price</code> 字段中的原始价格，这是商品的基础价格。</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="price-card display-price">
                    <h4>💰 用户显示价格</h4>
                    <p>根据用户费率计算后显示给用户的价格：<br><code>显示价格 = 原价 × 费率</code></p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="price-card rate-control">
                    <h4>⚙️ 费率说明</h4>
                    <p>
                        • 费率 = 1.0：显示原价<br>
                        • 费率 < 1.0：显示折扣价<br>
                        • 费率 > 1.0：显示加价
                    </p>
                </div>
            </div>
        </div>

        <!-- 商品价格对比表 -->
        <div class="price-card">
            <h3>📋 商品价格对比表</h3>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>商品ID</th>
                            <th>商品名称</th>
                            <th>数据库原价</th>
                            <th>运算方式</th>
                            <th>当前显示价格</th>
                            <th>价格差异</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $products = $DB->query("SELECT cid, name, price, yunsuan, status FROM qingka_wangke_class WHERE status=1 ORDER BY cid ASC LIMIT 20");
                        while ($product = $DB->fetch($products)):
                            $db_price = floatval($product['price']);
                            $operation = $product['yunsuan'] ?: '*';
                            $display_price = calculateUserPrice($db_price, $userrow['addprice'], $operation);
                            $price_diff = $display_price - $db_price;
                            
                            $diff_class = 'zero';
                            $diff_symbol = '';
                            if ($price_diff > 0) {
                                $diff_class = 'positive';
                                $diff_symbol = '+';
                            } elseif ($price_diff < 0) {
                                $diff_class = 'negative';
                            }
                        ?>
                        <tr>
                            <td><?php echo $product['cid']; ?></td>
                            <td><?php echo htmlspecialchars($product['name']); ?></td>
                            <td><strong><?php echo $db_price; ?></strong></td>
                            <td><?php echo $operation; ?></td>
                            <td><span class="highlight"><?php echo $display_price; ?></span></td>
                            <td class="price-diff <?php echo $diff_class; ?>">
                                <?php echo $diff_symbol . formatPrice($price_diff); ?>
                            </td>
                            <td>
                                <a href="../index/class.php?cid=<?php echo $product['cid']; ?>" 
                                   class="btn btn-xs btn-info" target="_blank">编辑</a>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
            <p class="text-muted">
                <small>
                    💡 提示：显示更多商品请访问 <a href="../index/class.php" target="_blank">商品管理页面</a>
                </small>
            </p>
        </div>

        <!-- 常见问题解答 -->
        <div class="price-card">
            <h3>❓ 常见问题解答</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4>Q: 为什么前端价格和数据库不一样？</h4>
                    <p><strong>A:</strong> 这是正常的！前端显示的是根据您的费率计算后的价格，不是数据库原价。</p>
                    
                    <h4>Q: 如何查看数据库原价？</h4>
                    <p><strong>A:</strong> 将费率设置为1.0，或者查看上面的对比表中的"数据库原价"列。</p>
                </div>
                <div class="col-md-6">
                    <h4>Q: 超级管理员可以设置任意费率吗？</h4>
                    <p><strong>A:</strong> 是的，超级管理员不受费率限制，可以设置任意值。</p>
                    
                    <h4>Q: 修改费率会影响其他用户吗？</h4>
                    <p><strong>A:</strong> 不会，这里只修改您自己的费率，不影响其他用户。</p>
                </div>
            </div>
        </div>

        <!-- 快速链接 -->
        <div class="price-card">
            <h3>🔗 相关管理页面</h3>
            <div class="row">
                <div class="col-md-3">
                    <a href="../index/class.php" class="btn btn-block btn-primary" target="_blank">
                        📦 商品管理
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../index/price.php" class="btn btn-block btn-info" target="_blank">
                        💰 价格表
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../index/userlist.php" class="btn btn-block btn-warning" target="_blank">
                        👥 用户管理
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../test/user_price_debug.php" class="btn btn-block btn-success" target="_blank">
                        🔍 价格调试
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/jquery.min.js"></script>
    <script src="../assets/js/bootstrap.min.js"></script>
    <script>
        function setRate(rate) {
            $('input[name="new_rate"]').val(rate);
        }
        
        // 自动提交表单（可选）
        function quickSetRate(rate) {
            $('input[name="new_rate"]').val(rate);
            $('form').submit();
        }
    </script>
</body>
</html>
