<?php
/**
 * 安全监控系统诊断工具
 */

echo "<h1>🔧 安全监控系统诊断</h1>";

// 1. 检查文件存在性
echo "<h2>📁 文件检查</h2>";
$files_to_check = [
    'security_simple.php' => '简化版安全监控',
    'security_monitor.php' => '完整版安全监控',
    'security_logs.php' => '安全日志查看器',
    'security_config.php' => '安全配置管理',
    'test_access.php' => '访问测试页面'
];

foreach ($files_to_check as $file => $desc) {
    $path = dirname(__FILE__) . '/' . $file;
    if (file_exists($path)) {
        echo "✅ {$desc} ({$file}) - 存在<br>";
    } else {
        echo "❌ {$desc} ({$file}) - 不存在<br>";
    }
}

// 2. 检查配置文件
echo "<h2>⚙️ 配置文件检查</h2>";
$config_path = dirname(__FILE__) . '/../confing/common.php';
if (file_exists($config_path)) {
    echo "✅ common.php - 存在<br>";
    
    // 尝试包含配置文件
    try {
        include($config_path);
        echo "✅ common.php - 包含成功<br>";
        
        // 检查关键变量
        if (isset($DB)) {
            echo "✅ 数据库对象 (\$DB) - 已定义<br>";
        } else {
            echo "❌ 数据库对象 (\$DB) - 未定义<br>";
        }
        
        if (isset($islogin)) {
            echo "✅ 登录状态 (\$islogin) - 已定义，值：{$islogin}<br>";
        } else {
            echo "❌ 登录状态 (\$islogin) - 未定义<br>";
        }
        
        if (isset($userrow)) {
            echo "✅ 用户信息 (\$userrow) - 已定义<br>";
            if (isset($userrow['uid'])) {
                echo "✅ 用户ID - {$userrow['uid']}<br>";
            }
            if (isset($userrow['user'])) {
                echo "✅ 用户名 - {$userrow['user']}<br>";
            }
        } else {
            echo "❌ 用户信息 (\$userrow) - 未定义<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ common.php - 包含失败：" . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ common.php - 不存在<br>";
}

// 3. 检查数据库表
echo "<h2>🗄️ 数据库表检查</h2>";
if (isset($DB)) {
    try {
        // 检查安全日志表
        $result = $DB->query("SHOW TABLES LIKE 'qingka_wangke_security_log'");
        if ($DB->fetch($result)) {
            echo "✅ qingka_wangke_security_log - 表存在<br>";
            
            // 检查表结构
            $structure = $DB->query("DESCRIBE qingka_wangke_security_log");
            echo "📋 表结构：<br>";
            while ($field = $DB->fetch($structure)) {
                echo "&nbsp;&nbsp;- {$field['Field']} ({$field['Type']})<br>";
            }
            
            // 检查记录数
            $count = $DB->count("SELECT COUNT(*) FROM qingka_wangke_security_log");
            echo "📊 记录数：{$count}<br>";
            
        } else {
            echo "❌ qingka_wangke_security_log - 表不存在<br>";
            echo "💡 建议执行以下SQL创建表：<br>";
            echo "<pre style='background:#f0f0f0;padding:10px;'>
CREATE TABLE `qingka_wangke_security_log` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `event_type` VARCHAR(32) NOT NULL,
    `ip` VARCHAR(45) NOT NULL,
    `user_agent` TEXT,
    `request_data` TEXT,
    `result` VARCHAR(16) NOT NULL,
    `details` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_event_type` (`event_type`),
    INDEX `idx_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
</pre>";
        }
        
        // 检查其他相关表
        $other_tables = [
            'qingka_wangke_recharge_limits' => '充值限制配置表',
            'qingka_wangke_recharge_config' => '充值配置表',
            'qingka_wangke_suspicious_transactions' => '可疑交易表'
        ];
        
        foreach ($other_tables as $table => $desc) {
            $result = $DB->query("SHOW TABLES LIKE '{$table}'");
            if ($DB->fetch($result)) {
                echo "✅ {$table} - {$desc}存在<br>";
            } else {
                echo "⚠️ {$table} - {$desc}不存在（可选）<br>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ 数据库查询失败：" . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ 无法检查数据库表（数据库对象未定义）<br>";
}

// 4. 权限检查
echo "<h2>🔐 权限检查</h2>";
if (isset($islogin) && isset($userrow)) {
    if ($islogin == 1) {
        echo "✅ 用户已登录<br>";
        if (isset($userrow['uid']) && $userrow['uid'] == 1) {
            echo "✅ 超级管理员权限<br>";
        } else {
            echo "⚠️ 非超级管理员用户（UID: {$userrow['uid']}）<br>";
        }
    } else {
        echo "❌ 用户未登录<br>";
    }
} else {
    echo "❌ 无法检查权限（登录状态未定义）<br>";
}

// 5. 系统信息
echo "<h2>💻 系统信息</h2>";
echo "PHP版本：" . PHP_VERSION . "<br>";
echo "当前时间：" . date('Y-m-d H:i:s') . "<br>";
echo "脚本路径：" . __FILE__ . "<br>";
echo "工作目录：" . getcwd() . "<br>";

// 6. 快速测试链接
echo "<h2>🔗 快速测试</h2>";
echo "<a href='test_access.php' target='_blank'>测试基本访问</a><br>";
echo "<a href='security_simple.php' target='_blank'>简化版安全监控</a><br>";
echo "<a href='security_monitor.php' target='_blank'>完整版安全监控</a><br>";
echo "<a href='security_logs.php' target='_blank'>安全日志查看</a><br>";
echo "<a href='security_config.php' target='_blank'>安全配置管理</a><br>";

echo "<h2>✅ 诊断完成</h2>";
echo "<p>如果发现问题，请根据上述检查结果进行修复。</p>";
?>
