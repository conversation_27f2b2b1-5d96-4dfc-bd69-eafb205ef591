<?php
/**
 * 测试页面访问
 */

echo "测试页面访问成功！";
echo "<br>当前时间：" . date('Y-m-d H:i:s');
echo "<br>当前路径：" . __FILE__;

// 测试包含common.php
try {
    include(dirname(__FILE__) . '/../confing/common.php');
    echo "<br>✅ common.php 包含成功";
    echo "<br>用户登录状态：" . ($islogin == 1 ? '已登录' : '未登录');
    if ($islogin == 1) {
        echo "<br>用户ID：" . $userrow['uid'];
        echo "<br>用户名：" . $userrow['user'];
    }
} catch (Exception $e) {
    echo "<br>❌ common.php 包含失败：" . $e->getMessage();
}
?>
