<?php
//由于skyriver天河平台可能存在项目价格经常变动，此代码每1小时同步1次最新价格和商品介绍，丢根目录下，就不用手动改价格跟商品介绍了
include('confing/common.php'); 

$hid = '52';//天河接口配置的hid
$beishu = 1;//价格倍数改数字
$skipCategories = array('1'); // 替换成您实际要跳过的 分类ID 格式以此类推

$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='$hid' ");
$skyriver_url = $a['url'].'/api.php?act=getclass';
$data = array("uid" => $a["user"], "key" => $a["pass"]);
$result = get_url($skyriver_url, $data);
$result = json_decode($result, true);
foreach ($result['data'] as $value) {
   // 如果 fenlei ID 在跳过列表中，则跳过
   if (in_array($value['fenlei'], $skipCategories)) {
   continue;
  }
     $danjia = $value['price'] * $beishu;
     $DB->query("update qingka_wangke_class set price='{$danjia}',content='{$value['content']}' where docking='$hid' and noun='{$value['cid']}' "); 
}
echo 'ok';


?>