# 🚀 天河(Skyriver)自动同步脚本使用说明

## 📋 功能概述

本脚本集成了天河平台的三大核心功能：
1. **商品价格同步** - 自动同步最新的商品价格和介绍
2. **商品克隆同步** - 自动上下架商品，支持分类映射
3. **订单进度同步** - 自动同步订单的学习进度和状态

## 🔧 安装步骤

### 1. 文件部署
将以下文件上传到您的网站根目录：
- `tianhe_auto_sync.php` - 主同步脚本
- `tianhe_config.php` - 配置文件

### 2. 权限设置
确保脚本有执行权限：
```bash
chmod +x tianhe_auto_sync.php
```

### 3. 创建日志目录
脚本会自动创建日志目录，但您也可以手动创建：
```bash
mkdir -p logs
chmod 755 logs
```

## ⚙️ 配置说明

### 基本配置
编辑 `tianhe_config.php` 文件：

```php
return [
    // 基本配置
    'tianhe_hid' => 52,                    // 天河接口HID（通常是52）
    'price_multiplier' => 1,               // 价格倍数（1=原价，2=2倍价格）
    
    // 商品同步配置
    'skip_categories' => ['1', '22', '9', '50'], // 跳过的分类ID：天河自营(22)、新源图图(9)、skyriver(1)
    'sync_product_enabled' => true,        // 是否启用商品同步
    'sync_product_clone_enabled' => true,  // 是否启用商品克隆同步
    'soft_delete_products' => true,        // true=下架商品(status=0), false=删除商品

    // 分类映射配置（天河分类ID => 本地分类ID）
    'category_mapping' => [
        '40' => '111',  // 天河ID:40 (900+继续教育) => 继教4
        '41' => '106',  // 天河ID:41 (900+易教育) => 易教育
        '49' => '104',  // 天河ID:49 (8090继续教育) => 8090edu
        '54' => '112',  // 天河ID:54 (学妹项目) => 学习通
        // 其他所有分类 => 杂(113)
    ],

    // 进度同步配置
    'sync_progress_enabled' => true,       // 是否启用进度同步
    'max_progress_batch' => 5000,          // 每批次最大同步数量
    
    // 其他配置...
];
```

### 重要配置项说明

| 配置项 | 说明 | 建议值 |
|--------|------|--------|
| `price_multiplier` | 价格倍数 | 1（原价）或根据需要调整 |
| `skip_categories` | 跳过的分类ID | 根据实际需要设置 |
| `max_progress_batch` | 进度同步批次大小 | 5000（默认值） |
| `api_timeout` | API超时时间 | 30秒 |
| `api_retry_times` | 重试次数 | 3次 |

## 🕐 宝塔计划任务设置

### 方法一：分别设置（推荐）

#### 商品价格同步（每小时执行）
- **任务类型**: Shell脚本
- **任务名称**: 天河商品同步
- **执行周期**: 每小时
- **脚本内容**:
```bash
/usr/bin/php /www/wwwroot/*************/tianhe_auto_sync.php product
```

#### 订单进度同步（每5分钟执行）
- **任务类型**: Shell脚本
- **任务名称**: 天河进度同步
- **执行周期**: 每5分钟
- **脚本内容**:
```bash
/usr/bin/php /www/wwwroot/*************/tianhe_auto_sync.php progress
```

### 方法二：Cron表达式设置

如果您熟悉Cron表达式，可以直接设置：

```bash
# 商品同步 - 每小时执行
0 * * * * /usr/bin/php /www/wwwroot/*************/tianhe_auto_sync.php product

# 进度同步 - 每5分钟执行
*/5 * * * * /usr/bin/php /www/wwwroot/*************/tianhe_auto_sync.php progress
```

## 🎯 使用方法

### 命令行使用
```bash
# 同步商品价格和说明
php tianhe_auto_sync.php product

# 商品克隆同步（上下架+分类映射）
php tianhe_auto_sync.php clone

# 同步订单进度
php tianhe_auto_sync.php progress

# 每日同步（推荐）
php tianhe_auto_sync.php daily

# 同步所有内容
php tianhe_auto_sync.php all

# 显示帮助信息
php tianhe_auto_sync.php help
```

### 手动测试
在设置计划任务前，建议先手动测试：

```bash
# 测试商品同步
cd /www/wwwroot/*************
php tianhe_auto_sync.php product

# 测试进度同步
php tianhe_auto_sync.php progress
```

## 📊 日志监控

### 日志文件位置
- 主日志: `logs/tianhe_sync.log`
- 时间戳文件: `logs/tianhe_last_sync.dat`

### 日志内容示例
```
[2025-09-01 10:00:01] [INFO] 天河同步脚本启动 - 操作: product
[2025-09-01 10:00:01] [INFO] 开始天河商品价格同步...
[2025-09-01 10:00:02] [INFO] 请求天河API: yyds.skyriver.top/api.php?act=getclass
[2025-09-01 10:00:05] [INFO] 商品同步完成 - 更新: 150, 跳过: 5, 错误: 0, 耗时: 3.2秒
```

### 查看日志
```bash
# 查看最新日志
tail -f logs/tianhe_sync.log

# 查看最近100行日志
tail -100 logs/tianhe_sync.log

# 搜索错误日志
grep "ERROR" logs/tianhe_sync.log
```

## 🔍 故障排除

### 常见问题

#### 1. 脚本无法执行
**症状**: 计划任务不运行或报错
**解决方案**:
- 检查PHP路径: `which php`
- 检查文件权限: `ls -la tianhe_auto_sync.php`
- 检查脚本路径是否正确

#### 2. API请求失败
**症状**: 日志显示"API请求失败"
**解决方案**:
- 检查天河接口配置是否正确
- 确认网络连接正常
- 检查天河平台是否正常运行
- 联系天河平台确认IP是否加白

#### 3. 数据库连接失败
**症状**: 日志显示数据库相关错误
**解决方案**:
- 检查 `confing/common.php` 配置
- 确认数据库服务正常运行
- 检查数据库用户权限

#### 4. 商品更新数量为0
**症状**: 同步完成但更新数量为0
**解决方案**:
- 检查 `skip_categories` 配置
- 确认天河平台有新的商品数据
- 检查商品表中是否存在对应的记录

### 调试模式
如需详细调试信息，可以临时修改配置：
```php
'log_level' => 'DEBUG',
'enable_performance_monitoring' => true,
```

## 📈 性能优化建议

### 1. 合理设置同步频率
- **商品同步**: 每小时一次即可（商品变化不频繁）
- **进度同步**: 每5-10分钟一次（根据业务需求调整）

### 2. 监控系统资源
- 定期检查日志文件大小
- 监控脚本执行时间
- 关注数据库性能

### 3. 网络优化
- 确保服务器网络稳定
- 如有条件，可申请天河平台IP白名单

## 🛡️ 安全注意事项

1. **文件权限**: 确保配置文件不被外部访问
2. **日志安全**: 定期清理敏感信息日志
3. **API安全**: 保护好天河接口的用户名和密码
4. **备份**: 定期备份重要配置和数据

## � 缓存问题解决方案

### 问题描述
如果发现数据库中有新商品，但前端搜索不出来，这是因为Redis缓存机制导致的。

### 自动解决
- ✅ 同步脚本已集成自动缓存清理功能
- ✅ 每次商品同步后自动清理相关缓存
- ✅ 用户可以立即搜索到新商品

### 手动解决
如果需要手动清理缓存：
```bash
# 手动清理所有商品缓存
php manual_cache_clear.php

# 或者直接清理Redis
redis-cli FLUSHDB
```

## 🆕 最新更新 (2025-09-01)

### 商品下架策略优化
根据天河平台最新源码，我们优化了商品下架策略：

#### 软删除模式（推荐，默认启用）
- **配置**: `'soft_delete_products' => true`
- **行为**: 天河删除的商品在本地设置为 `status=0`（下架状态）
- **优势**: 保留历史数据，避免数据丢失，可以恢复
- **适用**: 生产环境推荐使用

#### 硬删除模式
- **配置**: `'soft_delete_products' => false`
- **行为**: 天河删除的商品在本地直接删除记录
- **优势**: 数据库更干净，节省存储空间
- **风险**: 数据无法恢复

### 重新上架功能
- ✅ 自动检测天河商品重新上架
- ✅ 自动将本地下架商品重新上架
- ✅ 详细日志记录上架/下架操作

## �📞 技术支持

如遇到问题，请按以下步骤操作：

1. **查看日志**: 首先检查 `logs/tianhe_sync.log`
2. **手动测试**: 尝试手动执行脚本
3. **检查配置**: 确认所有配置项正确
4. **缓存问题**: 如果前端搜索不到新商品，运行 `php manual_cache_clear.php`
5. **联系支持**: 提供详细的错误日志和配置信息

---

**注意**: 本脚本已经过充分测试，但请在生产环境使用前先在测试环境验证功能正常。
