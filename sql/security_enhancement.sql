-- 充值系统安全加固数据库表结构
-- 执行前请备份数据库

-- 1. 防重放攻击表
CREATE TABLE IF NOT EXISTS `qingka_wangke_pay_nonce` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nonce` VARCHAR(64) NOT NULL UNIQUE COMMENT '随机数，防重放',
    `out_trade_no` VARCHAR(32) NOT NULL COMMENT '订单号',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_nonce` (`nonce`),
    INDEX `idx_created` (`created_at`),
    INDEX `idx_out_trade_no` (`out_trade_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='防重放攻击nonce表';

-- 2. 安全日志表
CREATE TABLE IF NOT EXISTS `qingka_wangke_security_log` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `event_type` VARCHAR(32) NOT NULL COMMENT '事件类型',
    `ip` VARCHAR(45) NOT NULL COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理',
    `request_data` TEXT COMMENT '请求数据',
    `result` VARCHAR(16) NOT NULL COMMENT '处理结果',
    `details` TEXT COMMENT '详细信息',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_event_type` (`event_type`),
    INDEX `idx_ip` (`ip`),
    INDEX `idx_result` (`result`),
    INDEX `idx_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全事件日志表';

-- 3. 充值限制配置表
CREATE TABLE IF NOT EXISTS `qingka_wangke_recharge_limits` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `limit_type` VARCHAR(32) NOT NULL COMMENT '限制类型：ip_minute, ip_hour, user_hour, user_day',
    `limit_value` INT NOT NULL COMMENT '限制数量',
    `description` VARCHAR(255) COMMENT '描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_limit_type` (`limit_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值频率限制配置表';

-- 4. 插入默认限制配置
INSERT INTO `qingka_wangke_recharge_limits` (`limit_type`, `limit_value`, `description`) VALUES
('ip_minute', 5, 'IP每分钟最大充值次数'),
('ip_hour', 20, 'IP每小时最大充值次数'),
('user_hour', 10, '用户每小时最大充值次数'),
('user_day', 50, '用户每天最大充值次数')
ON DUPLICATE KEY UPDATE 
    `limit_value` = VALUES(`limit_value`),
    `description` = VALUES(`description`);

-- 5. 充值金额配置表
CREATE TABLE IF NOT EXISTS `qingka_wangke_recharge_config` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `config_key` VARCHAR(32) NOT NULL COMMENT '配置键',
    `config_value` DECIMAL(10,2) NOT NULL COMMENT '配置值',
    `description` VARCHAR(255) COMMENT '描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值配置表';

-- 6. 插入默认充值配置
INSERT INTO `qingka_wangke_recharge_config` (`config_key`, `config_value`, `description`) VALUES
('min_amount', 1.00, '最小充值金额'),
('max_amount', 10000.00, '最大充值金额'),
('max_single_amount', 5000.00, '单次最大充值金额'),
('daily_max_amount', 20000.00, '每日最大充值金额')
ON DUPLICATE KEY UPDATE 
    `config_value` = VALUES(`config_value`),
    `description` = VALUES(`description`);

-- 7. 可疑交易记录表
CREATE TABLE IF NOT EXISTS `qingka_wangke_suspicious_transactions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `out_trade_no` VARCHAR(32) NOT NULL COMMENT '订单号',
    `uid` INT NOT NULL COMMENT '用户ID',
    `ip` VARCHAR(45) NOT NULL COMMENT 'IP地址',
    `amount` DECIMAL(10,2) NOT NULL COMMENT '金额',
    `reason` VARCHAR(255) NOT NULL COMMENT '可疑原因',
    `risk_level` TINYINT NOT NULL DEFAULT 1 COMMENT '风险等级：1低，2中，3高',
    `status` TINYINT DEFAULT 0 COMMENT '处理状态：0待处理，1已处理，2已忽略',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `processed_at` TIMESTAMP NULL,
    INDEX `idx_out_trade_no` (`out_trade_no`),
    INDEX `idx_uid` (`uid`),
    INDEX `idx_ip` (`ip`),
    INDEX `idx_risk_level` (`risk_level`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='可疑交易记录表';

-- 8. 为现有支付表添加安全字段（如果不存在）
ALTER TABLE `qingka_wangke_pay` 
ADD COLUMN IF NOT EXISTS `ip` VARCHAR(45) COMMENT 'IP地址' AFTER `domain`,
ADD COLUMN IF NOT EXISTS `user_agent` TEXT COMMENT '用户代理' AFTER `ip`,
ADD COLUMN IF NOT EXISTS `risk_score` TINYINT DEFAULT 0 COMMENT '风险评分' AFTER `user_agent`,
ADD INDEX IF NOT EXISTS `idx_ip` (`ip`),
ADD INDEX IF NOT EXISTS `idx_risk_score` (`risk_score`);

-- 9. 创建清理过期数据的存储过程
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `CleanExpiredSecurityData`()
BEGIN
    -- 清理30天前的nonce记录
    DELETE FROM `qingka_wangke_pay_nonce` 
    WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 清理90天前的安全日志
    DELETE FROM `qingka_wangke_security_log` 
    WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- 清理180天前的可疑交易记录
    DELETE FROM `qingka_wangke_suspicious_transactions` 
    WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 180 DAY) AND `status` != 0;
END //
DELIMITER ;

-- 10. 创建安全统计视图
CREATE OR REPLACE VIEW `v_security_stats` AS
SELECT 
    DATE(created_at) as date,
    event_type,
    result,
    COUNT(*) as count,
    COUNT(DISTINCT ip) as unique_ips
FROM `qingka_wangke_security_log`
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at), event_type, result
ORDER BY date DESC, event_type, result;

-- 11. 创建充值统计视图
CREATE OR REPLACE VIEW `v_recharge_stats` AS
SELECT 
    DATE(addtime) as date,
    COUNT(*) as total_orders,
    COUNT(CASE WHEN status = 1 THEN 1 END) as success_orders,
    SUM(CASE WHEN status = 1 THEN money ELSE 0 END) as total_amount,
    COUNT(DISTINCT uid) as unique_users,
    AVG(CASE WHEN status = 1 THEN money END) as avg_amount
FROM `qingka_wangke_pay`
WHERE addtime >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(addtime)
ORDER BY date DESC;

-- 12. 创建风险监控触发器
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `tr_pay_risk_check` 
AFTER INSERT ON `qingka_wangke_pay`
FOR EACH ROW
BEGIN
    DECLARE risk_score INT DEFAULT 0;
    DECLARE reason VARCHAR(255) DEFAULT '';
    
    -- 检查金额异常
    IF NEW.money > 5000 THEN
        SET risk_score = risk_score + 2;
        SET reason = CONCAT(reason, '大额充值;');
    END IF;
    
    -- 检查IP频率
    IF (SELECT COUNT(*) FROM qingka_wangke_pay 
        WHERE ip = NEW.ip AND addtime > DATE_SUB(NOW(), INTERVAL 1 HOUR)) > 5 THEN
        SET risk_score = risk_score + 1;
        SET reason = CONCAT(reason, 'IP频繁充值;');
    END IF;
    
    -- 如果有风险，记录到可疑交易表
    IF risk_score > 0 THEN
        INSERT INTO qingka_wangke_suspicious_transactions 
        (out_trade_no, uid, ip, amount, reason, risk_level, created_at)
        VALUES (NEW.out_trade_no, NEW.uid, NEW.ip, NEW.money, reason, 
                CASE WHEN risk_score >= 3 THEN 3 WHEN risk_score >= 2 THEN 2 ELSE 1 END,
                NOW());
    END IF;
END //
DELIMITER ;

-- 执行完成提示
SELECT '安全加固数据库表创建完成！' as message;
SELECT '请检查表结构是否正确创建' as reminder;
SELECT COUNT(*) as nonce_table_exists FROM information_schema.tables WHERE table_name = 'qingka_wangke_pay_nonce';
SELECT COUNT(*) as security_log_table_exists FROM information_schema.tables WHERE table_name = 'qingka_wangke_security_log';
