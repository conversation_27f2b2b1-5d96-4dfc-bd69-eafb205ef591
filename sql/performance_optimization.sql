-- 下单页面性能优化脚本
-- 解决分类选择和项目加载慢的问题

-- 1. 检查并添加必要的索引
-- 添加复合索引：status + fenlei（最重要的优化）
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND INDEX_NAME = 'idx_status_fenlei'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD INDEX idx_status_fenlei (status, fenlei)', 
    'SELECT "Index idx_status_fenlei already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加密价表索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_mijia' 
    AND INDEX_NAME = 'idx_uid_cid'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_mijia ADD INDEX idx_uid_cid (uid, cid)', 
    'SELECT "Index idx_uid_cid already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加质押记录表索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_zhiya_records' 
    AND INDEX_NAME = 'idx_uid_status'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_zhiya_records ADD INDEX idx_uid_status (uid, status)', 
    'SELECT "Index idx_uid_status already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 分析表统计信息
ANALYZE TABLE qingka_wangke_class;
ANALYZE TABLE qingka_wangke_mijia;
ANALYZE TABLE qingka_wangke_zhiya_records;
ANALYZE TABLE qingka_wangke_zhiya_config;

-- 3. 显示优化结果
SELECT 
    '性能优化完成' as message,
    NOW() as completion_time;

-- 显示各分类的项目数量统计
SELECT 
    f.name as category_name,
    f.id as category_id,
    COUNT(c.cid) as project_count,
    AVG(c.price) as avg_price
FROM qingka_wangke_fenlei f
LEFT JOIN qingka_wangke_class c ON c.fenlei = f.id AND c.status = 1
WHERE f.status = 1
GROUP BY f.id, f.name
ORDER BY project_count DESC;

-- 显示索引使用情况
SHOW INDEX FROM qingka_wangke_class;
