-- 第二步：创建配置表和数据

-- 3. 充值限制配置表
CREATE TABLE `qingka_wangke_recharge_limits` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `limit_type` VARCHAR(32) NOT NULL COMMENT '限制类型',
    `limit_value` INT NOT NULL COMMENT '限制数量',
    `description` VARCHAR(255) COMMENT '描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_limit_type` (`limit_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值频率限制配置表';

-- 4. 充值金额配置表
CREATE TABLE `qingka_wangke_recharge_config` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `config_key` VARCHAR(32) NOT NULL COMMENT '配置键',
    `config_value` DECIMAL(10,2) NOT NULL COMMENT '配置值',
    `description` VARCHAR(255) COMMENT '描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值配置表';

-- 5. 可疑交易记录表
CREATE TABLE `qingka_wangke_suspicious_transactions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `out_trade_no` VARCHAR(32) NOT NULL COMMENT '订单号',
    `uid` INT NOT NULL COMMENT '用户ID',
    `ip` VARCHAR(45) NOT NULL COMMENT 'IP地址',
    `amount` DECIMAL(10,2) NOT NULL COMMENT '金额',
    `reason` VARCHAR(255) NOT NULL COMMENT '可疑原因',
    `risk_level` TINYINT NOT NULL DEFAULT 1 COMMENT '风险等级：1低，2中，3高',
    `status` TINYINT DEFAULT 0 COMMENT '处理状态：0待处理，1已处理，2已忽略',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `processed_at` TIMESTAMP NULL,
    INDEX `idx_out_trade_no` (`out_trade_no`),
    INDEX `idx_uid` (`uid`),
    INDEX `idx_ip` (`ip`),
    INDEX `idx_risk_level` (`risk_level`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='可疑交易记录表';
