-- 第一步：创建基础安全表
-- 这些表不需要特殊权限，可以安全执行

-- 1. 防重放攻击表
CREATE TABLE `qingka_wangke_pay_nonce` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nonce` VARCHAR(64) NOT NULL UNIQUE COMMENT '随机数，防重放',
    `out_trade_no` VARCHAR(32) NOT NULL COMMENT '订单号',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_nonce` (`nonce`),
    INDEX `idx_created` (`created_at`),
    INDEX `idx_out_trade_no` (`out_trade_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='防重放攻击nonce表';

-- 2. 安全日志表
CREATE TABLE `qingka_wangke_security_log` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `event_type` VARCHAR(32) NOT NULL COMMENT '事件类型',
    `ip` VARCHAR(45) NOT NULL COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理',
    `request_data` TEXT COMMENT '请求数据',
    `result` VARCHAR(16) NOT NULL COMMENT '处理结果',
    `details` TEXT COMMENT '详细信息',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_event_type` (`event_type`),
    INDEX `idx_ip` (`ip`),
    INDEX `idx_result` (`result`),
    INDEX `idx_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全事件日志表';
