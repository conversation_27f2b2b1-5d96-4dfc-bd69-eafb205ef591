-- 超级性能优化脚本
-- 彻底解决下单页面加载慢的问题

-- 1. 创建覆盖索引（包含所有查询字段）
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_class' 
    AND INDEX_NAME = 'idx_covering_query'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_class ADD INDEX idx_covering_query (status, fenlei, sort, cid, name, price, yunsuan)', 
    'SELECT "Index idx_covering_query already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 优化密价表索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_mijia' 
    AND INDEX_NAME = 'idx_mijia_covering'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_mijia ADD INDEX idx_mijia_covering (uid, cid, price, mode)', 
    'SELECT "Index idx_mijia_covering already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 优化质押表索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_zhiya_records' 
    AND INDEX_NAME = 'idx_zhiya_covering'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_zhiya_records ADD INDEX idx_zhiya_covering (uid, status, config_id, id)', 
    'SELECT "Index idx_zhiya_covering already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 优化质押配置表索引
SET @index_exists = (
    SELECT COUNT(1) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'qingka_wangke_zhiya_config' 
    AND INDEX_NAME = 'idx_zhiya_config_covering'
);

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE qingka_wangke_zhiya_config ADD INDEX idx_zhiya_config_covering (id, category_id, discount_rate)', 
    'SELECT "Index idx_zhiya_config_covering already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 分析表统计信息
ANALYZE TABLE qingka_wangke_class;
ANALYZE TABLE qingka_wangke_mijia;
ANALYZE TABLE qingka_wangke_zhiya_records;
ANALYZE TABLE qingka_wangke_zhiya_config;
ANALYZE TABLE qingka_wangke_fenlei;

-- 6. 优化MySQL配置建议（需要管理员权限）
-- 这些是建议的配置，需要在my.cnf中设置
SELECT '建议的MySQL配置优化' as optimization_type, '
# 在 my.cnf 的 [mysqld] 部分添加以下配置：
innodb_buffer_pool_size = 256M
query_cache_size = 64M
query_cache_type = 1
tmp_table_size = 64M
max_heap_table_size = 64M
innodb_flush_log_at_trx_commit = 2
' as recommendations;

-- 7. 显示优化结果
SELECT 
    '超级性能优化完成' as message,
    NOW() as completion_time;

-- 8. 验证索引效果
EXPLAIN SELECT sort,cid,name,price,content,fenlei,yunsuan 
FROM qingka_wangke_class 
WHERE status=1 AND fenlei='104' 
ORDER BY sort ASC, cid ASC 
LIMIT 200;

-- 9. 显示各分类的项目数量和建议的缓存策略
SELECT 
    f.id as category_id,
    f.name as category_name,
    COUNT(c.cid) as project_count,
    CASE 
        WHEN COUNT(c.cid) > 500 THEN '建议缓存30分钟'
        WHEN COUNT(c.cid) > 100 THEN '建议缓存15分钟'
        ELSE '建议缓存5分钟'
    END as cache_strategy,
    CASE 
        WHEN COUNT(c.cid) > 1000 THEN '超大分类，建议分页'
        WHEN COUNT(c.cid) > 500 THEN '大分类，限制200项'
        ELSE '正常分类'
    END as load_strategy
FROM qingka_wangke_fenlei f
LEFT JOIN qingka_wangke_class c ON c.fenlei = f.id AND c.status = 1
WHERE f.status = 1
GROUP BY f.id, f.name
ORDER BY project_count DESC;

-- 10. 清理建议
SELECT '数据库清理建议' as cleanup_type, '
-- 清理无效的密价记录
DELETE m FROM qingka_wangke_mijia m 
LEFT JOIN qingka_wangke_class c ON c.cid = m.cid 
WHERE c.cid IS NULL OR c.status = 0;

-- 清理无效的质押记录
DELETE zr FROM qingka_wangke_zhiya_records zr 
LEFT JOIN qingka_wangke_user u ON u.uid = zr.uid 
WHERE u.uid IS NULL;
' as cleanup_sql;
