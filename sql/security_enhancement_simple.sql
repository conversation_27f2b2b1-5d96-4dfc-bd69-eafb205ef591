-- 充值系统安全加固数据库表结构（简化版）
-- 适用于普通数据库用户权限
-- 执行前请备份数据库

-- 1. 防重放攻击表
CREATE TABLE IF NOT EXISTS `qingka_wangke_pay_nonce` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `nonce` VARCHAR(64) NOT NULL UNIQUE COMMENT '随机数，防重放',
    `out_trade_no` VARCHAR(32) NOT NULL COMMENT '订单号',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_nonce` (`nonce`),
    INDEX `idx_created` (`created_at`),
    INDEX `idx_out_trade_no` (`out_trade_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='防重放攻击nonce表';

-- 2. 安全日志表
CREATE TABLE IF NOT EXISTS `qingka_wangke_security_log` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `event_type` VARCHAR(32) NOT NULL COMMENT '事件类型',
    `ip` VARCHAR(45) NOT NULL COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理',
    `request_data` TEXT COMMENT '请求数据',
    `result` VARCHAR(16) NOT NULL COMMENT '处理结果',
    `details` TEXT COMMENT '详细信息',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_event_type` (`event_type`),
    INDEX `idx_ip` (`ip`),
    INDEX `idx_result` (`result`),
    INDEX `idx_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全事件日志表';

-- 3. 充值限制配置表
CREATE TABLE IF NOT EXISTS `qingka_wangke_recharge_limits` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `limit_type` VARCHAR(32) NOT NULL COMMENT '限制类型：ip_minute, ip_hour, user_hour, user_day',
    `limit_value` INT NOT NULL COMMENT '限制数量',
    `description` VARCHAR(255) COMMENT '描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_limit_type` (`limit_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值频率限制配置表';

-- 4. 插入默认限制配置
INSERT INTO `qingka_wangke_recharge_limits` (`limit_type`, `limit_value`, `description`) VALUES
('ip_minute', 5, 'IP每分钟最大充值次数'),
('ip_hour', 20, 'IP每小时最大充值次数'),
('user_hour', 10, '用户每小时最大充值次数'),
('user_day', 50, '用户每天最大充值次数')
ON DUPLICATE KEY UPDATE 
    `limit_value` = VALUES(`limit_value`),
    `description` = VALUES(`description`);

-- 5. 充值金额配置表
CREATE TABLE IF NOT EXISTS `qingka_wangke_recharge_config` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `config_key` VARCHAR(32) NOT NULL COMMENT '配置键',
    `config_value` DECIMAL(10,2) NOT NULL COMMENT '配置值',
    `description` VARCHAR(255) COMMENT '描述',
    `status` TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值配置表';

-- 6. 插入默认充值配置
INSERT INTO `qingka_wangke_recharge_config` (`config_key`, `config_value`, `description`) VALUES
('min_amount', 1.00, '最小充值金额'),
('max_amount', 10000.00, '最大充值金额'),
('max_single_amount', 5000.00, '单次最大充值金额'),
('daily_max_amount', 20000.00, '每日最大充值金额')
ON DUPLICATE KEY UPDATE 
    `config_value` = VALUES(`config_value`),
    `description` = VALUES(`description`);

-- 7. 可疑交易记录表
CREATE TABLE IF NOT EXISTS `qingka_wangke_suspicious_transactions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `out_trade_no` VARCHAR(32) NOT NULL COMMENT '订单号',
    `uid` INT NOT NULL COMMENT '用户ID',
    `ip` VARCHAR(45) NOT NULL COMMENT 'IP地址',
    `amount` DECIMAL(10,2) NOT NULL COMMENT '金额',
    `reason` VARCHAR(255) NOT NULL COMMENT '可疑原因',
    `risk_level` TINYINT NOT NULL DEFAULT 1 COMMENT '风险等级：1低，2中，3高',
    `status` TINYINT DEFAULT 0 COMMENT '处理状态：0待处理，1已处理，2已忽略',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `processed_at` TIMESTAMP NULL,
    INDEX `idx_out_trade_no` (`out_trade_no`),
    INDEX `idx_uid` (`uid`),
    INDEX `idx_ip` (`ip`),
    INDEX `idx_risk_level` (`risk_level`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='可疑交易记录表';

-- 8. 检查现有支付表是否需要添加字段
-- 注意：如果字段已存在，这些语句会报错，但不影响功能
-- 您可以手动检查 qingka_wangke_pay 表是否已有这些字段

-- 添加IP地址字段
-- ALTER TABLE `qingka_wangke_pay` ADD COLUMN `ip` VARCHAR(45) COMMENT 'IP地址';

-- 添加用户代理字段  
-- ALTER TABLE `qingka_wangke_pay` ADD COLUMN `user_agent` TEXT COMMENT '用户代理';

-- 添加风险评分字段
-- ALTER TABLE `qingka_wangke_pay` ADD COLUMN `risk_score` TINYINT DEFAULT 0 COMMENT '风险评分';

-- 添加索引
-- ALTER TABLE `qingka_wangke_pay` ADD INDEX `idx_ip` (`ip`);
-- ALTER TABLE `qingka_wangke_pay` ADD INDEX `idx_risk_score` (`risk_score`);

-- 执行完成提示
SELECT '安全加固数据库表创建完成！' as message;
SELECT '如需添加支付表字段，请手动执行注释中的ALTER语句' as reminder;
