<?php
/**
 * 路由测试页面
 */

// 引入公共配置文件
include("../confing/common.php");

// 检查登录状态和权限
if ($islogin != 1 || $userrow['uid'] != 1) {
    die('仅超级管理员可访问此页面');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>路由测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: #28a745; font-weight: bold; }
        .info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1 class="success">✅ 路由测试成功！</h1>
    
    <div class="info">
        <h3>页面信息</h3>
        <p><strong>文件路径：</strong> <?php echo __FILE__; ?></p>
        <p><strong>相对路径：</strong> index/test_route.php</p>
        <p><strong>访问时间：</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        <p><strong>用户权限：</strong> 超级管理员 (UID: <?php echo $userrow['uid']; ?>)</p>
    </div>

    <div class="info">
        <h3>系统状态</h3>
        <p>✅ 配置文件加载成功</p>
        <p>✅ 数据库连接正常</p>
        <p>✅ 用户权限验证通过</p>
        <p>✅ 页面路由工作正常</p>
    </div>

    <div class="info">
        <h3>说明</h3>
        <p>如果您能看到这个页面，说明：</p>
        <ul>
            <li>菜单系统的路由机制工作正常</li>
            <li>index/ 目录下的PHP文件可以正确加载</li>
            <li>安全监控页面应该也能正常工作</li>
        </ul>
    </div>

    <div class="info">
        <h3>下一步</h3>
        <p>现在可以测试安全监控页面：</p>
        <ul>
            <li>点击侧边栏的"安全监控" → "安全监控（简化版）"</li>
            <li>点击侧边栏的"安全监控" → "安全监控面板"</li>
            <li>点击侧边栏的"安全监控" → "安全日志查看"</li>
            <li>点击侧边栏的"安全监控" → "安全配置管理"</li>
        </ul>
    </div>

</body>
</html>
