<?php
/**
 * 简化版安全监控页面
 * 确保基本功能正常工作
 */

// 引入公共配置文件
include("../confing/common.php");

// 检查登录状态和权限
if ($islogin != 1 || $userrow['uid'] != 1) {
    die('仅超级管理员可访问此页面');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>安全监控面板</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .info-box { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #ddd; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ 安全监控面板（简化版）</h1>
        <p>系统安全状态监控</p>
    </div>

    <div class="info-box">
        <h3>📊 系统状态</h3>
        <table>
            <tr>
                <th>检查项目</th>
                <th>状态</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>数据库连接</td>
                <td class="success">✅ 正常</td>
                <td>数据库连接正常</td>
            </tr>
            <tr>
                <td>用户权限</td>
                <td class="success">✅ 正常</td>
                <td>超级管理员权限验证通过</td>
            </tr>
            <tr>
                <td>安全日志表</td>
                <td>
                    <?php
                    try {
                        $table_check = $DB->query("SHOW TABLES LIKE 'qingka_wangke_security_log'");
                        if ($DB->fetch($table_check)) {
                            echo '<span class="success">✅ 存在</span>';
                            $table_exists = true;
                        } else {
                            echo '<span class="warning">⚠️ 不存在</span>';
                            $table_exists = false;
                        }
                    } catch (Exception $e) {
                        echo '<span class="error">❌ 检查失败</span>';
                        $table_exists = false;
                    }
                    ?>
                </td>
                <td>
                    <?php if ($table_exists): ?>
                        安全日志表已创建
                    <?php else: ?>
                        需要创建安全日志表
                    <?php endif; ?>
                </td>
            </tr>
        </table>
    </div>

    <?php if ($table_exists): ?>
    <div class="info-box">
        <h3>📋 最近安全事件</h3>
        <table>
            <tr>
                <th>时间</th>
                <th>事件类型</th>
                <th>IP地址</th>
                <th>结果</th>
                <th>详情</th>
            </tr>
            <?php
            try {
                $logs = $DB->query("SELECT * FROM qingka_wangke_security_log ORDER BY created_at DESC LIMIT 10");
                $log_count = 0;
                while ($log = $DB->fetch($logs)) {
                    $log_count++;
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($log['created_at']) . "</td>";
                    echo "<td>" . htmlspecialchars($log['event_type']) . "</td>";
                    echo "<td>" . htmlspecialchars($log['ip']) . "</td>";
                    echo "<td>" . htmlspecialchars($log['result']) . "</td>";
                    echo "<td>" . htmlspecialchars(substr($log['details'], 0, 50)) . "...</td>";
                    echo "</tr>";
                }
                
                if ($log_count == 0) {
                    echo "<tr><td colspan='5' style='text-align: center; color: #666;'>暂无安全事件记录</td></tr>";
                }
            } catch (Exception $e) {
                echo "<tr><td colspan='5' style='text-align: center; color: #dc3545;'>查询失败：" . htmlspecialchars($e->getMessage()) . "</td></tr>";
            }
            ?>
        </table>
    </div>
    <?php else: ?>
    <div class="info-box">
        <h3>⚠️ 安全日志表不存在</h3>
        <p>请执行以下SQL语句创建安全日志表：</p>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">
CREATE TABLE `qingka_wangke_security_log` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `event_type` VARCHAR(32) NOT NULL,
    `ip` VARCHAR(45) NOT NULL,
    `user_agent` TEXT,
    `request_data` TEXT,
    `result` VARCHAR(16) NOT NULL,
    `details` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_event_type` (`event_type`),
    INDEX `idx_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        </pre>
    </div>
    <?php endif; ?>

    <div class="info-box">
        <h3>🔗 相关工具</h3>
        <ul>
            <li><a href="../admin/security_monitor.php" target="_blank">完整版安全监控面板</a></li>
            <li><a href="../admin/security_logs.php" target="_blank">安全日志查看器</a></li>
            <li><a href="../admin/security_config.php" target="_blank">安全配置管理</a></li>
            <li><a href="../admin/test_access.php" target="_blank">访问测试页面</a></li>
        </ul>
    </div>

    <div class="info-box">
        <h3>📝 使用说明</h3>
        <ol>
            <li>如果安全日志表不存在，请先执行上面的SQL语句创建表</li>
            <li>创建表后，充值系统会自动记录安全事件</li>
            <li>可以通过完整版监控面板查看详细统计</li>
            <li>建议定期检查安全日志，关注异常事件</li>
        </ol>
    </div>

</body>
</html>
