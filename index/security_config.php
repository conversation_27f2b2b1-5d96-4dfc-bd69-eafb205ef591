<?php
/**
 * 安全配置管理页面
 * 管理充值安全相关配置
 */

// 引入公共配置文件
include("../confing/common.php");

// 检查登录状态和权限
if ($islogin != 1 || $userrow['uid'] != 1) {
    die('仅超级管理员可访问此页面');
}

$message = '';
$error = '';

// 处理表单提交
if ($_POST) {
    try {
        // 这里可以添加配置保存逻辑
        // 例如保存到数据库或配置文件
        $message = '配置保存成功！';
    } catch (Exception $e) {
        $error = '配置保存失败：' . $e->getMessage();
    }
}

// 默认配置值（实际应用中应该从数据库或配置文件读取）
$config = [
    'enable_security_log' => true,
    'max_payment_amount' => 10000,
    'rate_limit_enabled' => true,
    'rate_limit_requests' => 100,
    'rate_limit_window' => 3600,
    'ip_whitelist' => '',
    'ip_blacklist' => '',
    'enable_geo_blocking' => false,
    'blocked_countries' => '',
    'enable_user_agent_check' => true,
    'suspicious_user_agents' => 'bot,crawler,spider',
    'enable_honeypot' => false,
    'honeypot_fields' => 'email_confirm,phone_backup',
    'alert_email' => '',
    'alert_threshold' => 10
];

?>
<!DOCTYPE html>
<html>
<head>
    <title>安全配置管理</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .config-section { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .config-section h3 { margin-top: 0; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #555; }
        .form-group input, .form-group textarea, .form-group select { 
            width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; 
        }
        .form-group textarea { height: 80px; resize: vertical; }
        .form-group .help-text { font-size: 0.9em; color: #666; margin-top: 5px; }
        
        .checkbox-group { display: flex; align-items: center; }
        .checkbox-group input[type="checkbox"] { width: auto; margin-right: 10px; }
        
        .btn { padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; margin-left: 10px; }
        .btn:hover { opacity: 0.9; }
        
        .alert { padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        .config-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        @media (max-width: 768px) { .config-grid { grid-template-columns: 1fr; } }
        
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 8px; }
        .status-enabled { background: #28a745; }
        .status-disabled { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ 安全配置管理</h1>
            <p>配置充值系统的安全参数和防护策略</p>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-success">
            ✅ <?php echo $message; ?>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger">
            ❌ <?php echo $error; ?>
        </div>
        <?php endif; ?>

        <form method="POST">
            <div class="config-grid">
                <div class="config-section">
                    <h3>🔒 基础安全设置</h3>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="enable_security_log" name="enable_security_log" 
                                   <?php echo $config['enable_security_log'] ? 'checked' : ''; ?>>
                            <label for="enable_security_log">启用安全日志记录</label>
                        </div>
                        <div class="help-text">记录所有安全相关事件到数据库</div>
                    </div>

                    <div class="form-group">
                        <label for="max_payment_amount">单笔充值最大金额 (元)</label>
                        <input type="number" id="max_payment_amount" name="max_payment_amount" 
                               value="<?php echo $config['max_payment_amount']; ?>" min="1" max="100000">
                        <div class="help-text">超过此金额的充值将被标记为可疑</div>
                    </div>

                    <div class="form-group">
                        <label for="alert_email">安全警报邮箱</label>
                        <input type="email" id="alert_email" name="alert_email" 
                               value="<?php echo $config['alert_email']; ?>" placeholder="<EMAIL>">
                        <div class="help-text">接收安全警报的邮箱地址</div>
                    </div>

                    <div class="form-group">
                        <label for="alert_threshold">警报阈值</label>
                        <input type="number" id="alert_threshold" name="alert_threshold" 
                               value="<?php echo $config['alert_threshold']; ?>" min="1" max="1000">
                        <div class="help-text">每小时失败次数超过此值时发送警报</div>
                    </div>
                </div>

                <div class="config-section">
                    <h3>🚦 频率限制设置</h3>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="rate_limit_enabled" name="rate_limit_enabled" 
                                   <?php echo $config['rate_limit_enabled'] ? 'checked' : ''; ?>>
                            <label for="rate_limit_enabled">启用频率限制</label>
                        </div>
                        <div class="help-text">限制单个IP的请求频率</div>
                    </div>

                    <div class="form-group">
                        <label for="rate_limit_requests">时间窗口内最大请求数</label>
                        <input type="number" id="rate_limit_requests" name="rate_limit_requests" 
                               value="<?php echo $config['rate_limit_requests']; ?>" min="1" max="10000">
                        <div class="help-text">单个IP在时间窗口内允许的最大请求数</div>
                    </div>

                    <div class="form-group">
                        <label for="rate_limit_window">时间窗口 (秒)</label>
                        <input type="number" id="rate_limit_window" name="rate_limit_window" 
                               value="<?php echo $config['rate_limit_window']; ?>" min="60" max="86400">
                        <div class="help-text">频率限制的时间窗口，默认3600秒(1小时)</div>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h3>🌐 IP地址管理</h3>
                
                <div class="config-grid">
                    <div class="form-group">
                        <label for="ip_whitelist">IP白名单</label>
                        <textarea id="ip_whitelist" name="ip_whitelist" 
                                  placeholder="每行一个IP地址或IP段&#10;例如：&#10;***********&#10;10.0.0.0/8"><?php echo $config['ip_whitelist']; ?></textarea>
                        <div class="help-text">白名单中的IP地址将跳过所有安全检查</div>
                    </div>

                    <div class="form-group">
                        <label for="ip_blacklist">IP黑名单</label>
                        <textarea id="ip_blacklist" name="ip_blacklist" 
                                  placeholder="每行一个IP地址或IP段&#10;例如：&#10;*************&#10;**********/12"><?php echo $config['ip_blacklist']; ?></textarea>
                        <div class="help-text">黑名单中的IP地址将被直接拒绝</div>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h3>🔍 高级检测设置</h3>
                
                <div class="config-grid">
                    <div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="enable_user_agent_check" name="enable_user_agent_check" 
                                       <?php echo $config['enable_user_agent_check'] ? 'checked' : ''; ?>>
                                <label for="enable_user_agent_check">启用User-Agent检查</label>
                            </div>
                            <div class="help-text">检查可疑的User-Agent字符串</div>
                        </div>

                        <div class="form-group">
                            <label for="suspicious_user_agents">可疑User-Agent关键词</label>
                            <textarea id="suspicious_user_agents" name="suspicious_user_agents" 
                                      placeholder="用逗号分隔关键词"><?php echo $config['suspicious_user_agents']; ?></textarea>
                            <div class="help-text">包含这些关键词的User-Agent将被标记</div>
                        </div>
                    </div>

                    <div>
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="enable_honeypot" name="enable_honeypot" 
                                       <?php echo $config['enable_honeypot'] ? 'checked' : ''; ?>>
                                <label for="enable_honeypot">启用蜜罐字段</label>
                            </div>
                            <div class="help-text">添加隐藏字段来检测机器人</div>
                        </div>

                        <div class="form-group">
                            <label for="honeypot_fields">蜜罐字段名称</label>
                            <input type="text" id="honeypot_fields" name="honeypot_fields" 
                                   value="<?php echo $config['honeypot_fields']; ?>" 
                                   placeholder="用逗号分隔字段名">
                            <div class="help-text">这些隐藏字段如果被填写将触发警报</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h3>📊 当前状态</h3>
                <div class="config-grid">
                    <div>
                        <p><span class="status-indicator <?php echo $config['enable_security_log'] ? 'status-enabled' : 'status-disabled'; ?>"></span>
                           安全日志：<?php echo $config['enable_security_log'] ? '已启用' : '已禁用'; ?></p>
                        <p><span class="status-indicator <?php echo $config['rate_limit_enabled'] ? 'status-enabled' : 'status-disabled'; ?>"></span>
                           频率限制：<?php echo $config['rate_limit_enabled'] ? '已启用' : '已禁用'; ?></p>
                    </div>
                    <div>
                        <p><span class="status-indicator <?php echo $config['enable_user_agent_check'] ? 'status-enabled' : 'status-disabled'; ?>"></span>
                           User-Agent检查：<?php echo $config['enable_user_agent_check'] ? '已启用' : '已禁用'; ?></p>
                        <p><span class="status-indicator <?php echo $config['enable_honeypot'] ? 'status-enabled' : 'status-disabled'; ?>"></span>
                           蜜罐检测：<?php echo $config['enable_honeypot'] ? '已启用' : '已禁用'; ?></p>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <button type="submit" class="btn btn-primary">保存配置</button>
                <button type="button" class="btn btn-secondary" onclick="location.reload()">重置</button>
            </div>
        </form>
    </div>

    <script>
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const maxAmount = document.getElementById('max_payment_amount').value;
            const alertThreshold = document.getElementById('alert_threshold').value;
            
            if (maxAmount <= 0) {
                alert('最大充值金额必须大于0');
                e.preventDefault();
                return;
            }
            
            if (alertThreshold <= 0) {
                alert('警报阈值必须大于0');
                e.preventDefault();
                return;
            }
            
            // 确认保存
            if (!confirm('确定要保存这些配置吗？')) {
                e.preventDefault();
            }
        });

        // 动态启用/禁用相关字段
        document.getElementById('rate_limit_enabled').addEventListener('change', function() {
            const enabled = this.checked;
            document.getElementById('rate_limit_requests').disabled = !enabled;
            document.getElementById('rate_limit_window').disabled = !enabled;
        });

        document.getElementById('enable_user_agent_check').addEventListener('change', function() {
            const enabled = this.checked;
            document.getElementById('suspicious_user_agents').disabled = !enabled;
        });

        document.getElementById('enable_honeypot').addEventListener('change', function() {
            const enabled = this.checked;
            document.getElementById('honeypot_fields').disabled = !enabled;
        });

        // 初始化字段状态
        document.getElementById('rate_limit_enabled').dispatchEvent(new Event('change'));
        document.getElementById('enable_user_agent_check').dispatchEvent(new Event('change'));
        document.getElementById('enable_honeypot').dispatchEvent(new Event('change'));
    </script>
</body>
</html>
