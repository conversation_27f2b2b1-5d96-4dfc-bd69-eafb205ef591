<?php
/**
 * 安全日志查看器
 * 详细查看和分析安全日志
 */

// 引入公共配置文件
include("../confing/common.php");

// 检查登录状态和权限
if ($islogin != 1 || $userrow['uid'] != 1) {
    die('仅超级管理员可访问此页面');
}

// 检查安全日志表是否存在
try {
    $table_check = $DB->query("SHOW TABLES LIKE 'qingka_wangke_security_log'");
    if (!$DB->fetch($table_check)) {
        // 自动创建安全日志表
        $create_sql = "CREATE TABLE `qingka_wangke_security_log` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `event_type` VARCHAR(32) NOT NULL,
            `ip` VARCHAR(45) NOT NULL,
            `user_agent` TEXT,
            `request_data` TEXT,
            `result` VARCHAR(16) NOT NULL,
            `details` TEXT,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX `idx_event_type` (`event_type`),
            INDEX `idx_created` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $DB->query($create_sql);
        $table_created = true;
    }
} catch (Exception $e) {
    $table_error = "安全日志表不存在且无法创建：" . $e->getMessage();
}

// 获取查询参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 50;
$offset = ($page - 1) * $limit;

$event_type = $_GET['event_type'] ?? '';
$result = $_GET['result'] ?? '';
$ip = $_GET['ip'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// 构建查询条件
$where_conditions = [];
$params = [];

if ($event_type) {
    $where_conditions[] = "event_type = ?";
    $params[] = $event_type;
}

if ($result) {
    $where_conditions[] = "result = ?";
    $params[] = $result;
}

if ($ip) {
    $where_conditions[] = "ip LIKE ?";
    $params[] = "%{$ip}%";
}

if ($date_from) {
    $where_conditions[] = "DATE(created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = empty($where_conditions) ? '' : 'WHERE ' . implode(' AND ', $where_conditions);

$total = 0;
$logs = null;

if (!isset($table_error)) {
    try {
        // 查询总数
        $count_sql = "SELECT COUNT(*) FROM qingka_wangke_security_log {$where_clause}";
        $total = $DB->prepare_count($count_sql, $params);

        // 查询日志数据 - 添加LIMIT参数到params数组
        $sql = "SELECT * FROM qingka_wangke_security_log {$where_clause} ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $query_params = array_merge($params, [$limit, $offset]);
        $logs_stmt = $DB->prepare_query($sql, $query_params);

        if ($logs_stmt) {
            $logs_result = $logs_stmt->get_result();
        } else {
            throw new Exception("查询语句执行失败");
        }
    } catch (Exception $e) {
        $query_error = "查询失败：" . $e->getMessage();
    }
}

// 计算分页
$total_pages = ceil($total / $limit);

?>
<!DOCTYPE html>
<html>
<head>
    <title>安全日志查看器</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .filters { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #ddd; }
        .filter-row { display: flex; gap: 15px; margin-bottom: 15px; align-items: center; flex-wrap: wrap; }
        .filter-group { display: flex; flex-direction: column; }
        .filter-group label { font-size: 0.9em; color: #666; margin-bottom: 5px; }
        .filter-group input, .filter-group select { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }
        
        .logs-table { width: 100%; border-collapse: collapse; background: #fff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .logs-table th, .logs-table td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        .logs-table th { background: #f8f9fa; font-weight: bold; }
        .logs-table tr:hover { background: #f8f9fa; }
        
        .status-success { color: #28a745; font-weight: bold; }
        .status-fail { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        
        .pagination { margin: 20px 0; text-align: center; }
        .pagination a { padding: 8px 12px; margin: 0 2px; text-decoration: none; border: 1px solid #ddd; border-radius: 4px; }
        .pagination a.current { background: #007bff; color: white; border-color: #007bff; }
        .pagination a:hover:not(.current) { background: #f8f9fa; }
        
        .details-cell { max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .details-cell:hover { white-space: normal; overflow: visible; }
        
        .stats-summary { background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .stats-summary strong { color: #1976d2; }
        
        .error-box { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 安全日志查看器</h1>
        <p>查看和分析系统安全事件日志</p>
    </div>

    <?php if (isset($table_error)): ?>
    <div class="error-box">
        <h3>⚠️ 数据库表错误</h3>
        <p><?php echo $table_error; ?></p>
    </div>
    <?php elseif (isset($query_error)): ?>
    <div class="error-box">
        <h3>⚠️ 查询错误</h3>
        <p><?php echo $query_error; ?></p>
    </div>
    <?php else: ?>

    <div class="stats-summary">
        <strong>查询结果：</strong> 共找到 <?php echo $total; ?> 条记录，当前显示第 <?php echo $page; ?> 页，共 <?php echo $total_pages; ?> 页
    </div>

    <div class="filters">
        <form method="GET">
            <div class="filter-row">
                <div class="filter-group">
                    <label>事件类型</label>
                    <select name="event_type">
                        <option value="">全部</option>
                        <option value="payment_notify" <?php echo $event_type == 'payment_notify' ? 'selected' : ''; ?>>支付通知</option>
                        <option value="security_check" <?php echo $event_type == 'security_check' ? 'selected' : ''; ?>>安全检查</option>
                        <option value="rate_limit" <?php echo $event_type == 'rate_limit' ? 'selected' : ''; ?>>频率限制</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>处理结果</label>
                    <select name="result">
                        <option value="">全部</option>
                        <option value="success" <?php echo $result == 'success' ? 'selected' : ''; ?>>成功</option>
                        <option value="fail" <?php echo $result == 'fail' ? 'selected' : ''; ?>>失败</option>
                        <option value="blocked" <?php echo $result == 'blocked' ? 'selected' : ''; ?>>阻止</option>
                        <option value="warning" <?php echo $result == 'warning' ? 'selected' : ''; ?>>警告</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>IP地址</label>
                    <input type="text" name="ip" value="<?php echo htmlspecialchars($ip); ?>" placeholder="输入IP地址">
                </div>
            </div>
            
            <div class="filter-row">
                <div class="filter-group">
                    <label>开始日期</label>
                    <input type="date" name="date_from" value="<?php echo $date_from; ?>">
                </div>
                
                <div class="filter-group">
                    <label>结束日期</label>
                    <input type="date" name="date_to" value="<?php echo $date_to; ?>">
                </div>
                
                <div class="filter-group" style="justify-content: flex-end; margin-top: 20px;">
                    <button type="submit" class="btn btn-primary">查询</button>
                    <a href="?" class="btn btn-secondary">重置</a>
                </div>
            </div>
        </form>
    </div>

    <table class="logs-table">
        <thead>
            <tr>
                <th>时间</th>
                <th>事件类型</th>
                <th>IP地址</th>
                <th>结果</th>
                <th>详细信息</th>
                <th>请求数据</th>
            </tr>
        </thead>
        <tbody>
            <?php if ($total == 0): ?>
            <tr>
                <td colspan="6" style="text-align: center; padding: 40px; color: #666;">
                    没有找到符合条件的日志记录
                </td>
            </tr>
            <?php elseif (isset($query_error)): ?>
            <tr>
                <td colspan="6" style="text-align: center; padding: 40px; color: #dc3545;">
                    <?php echo $query_error; ?>
                </td>
            </tr>
            <?php else: ?>
                <?php while ($log = $logs_result->fetch_assoc()): ?>
                <tr>
                    <td><?php echo htmlspecialchars($log['created_at']); ?></td>
                    <td><?php echo htmlspecialchars($log['event_type']); ?></td>
                    <td><?php echo htmlspecialchars($log['ip']); ?></td>
                    <td class="status-<?php echo htmlspecialchars($log['result']); ?>">
                        <?php echo htmlspecialchars($log['result']); ?>
                    </td>
                    <td class="details-cell" title="<?php echo htmlspecialchars($log['details'] ?? ''); ?>">
                        <?php echo htmlspecialchars($log['details'] ?? ''); ?>
                    </td>
                    <td class="details-cell" title="<?php echo htmlspecialchars($log['request_data'] ?? ''); ?>">
                        <?php
                        $request_data = $log['request_data'] ?? '';
                        if (strlen($request_data) > 50) {
                            echo htmlspecialchars(substr($request_data, 0, 50)) . '...';
                        } else {
                            echo htmlspecialchars($request_data);
                        }
                        ?>
                    </td>
                </tr>
                <?php endwhile; ?>
            <?php endif; ?>
        </tbody>
    </table>

    <?php if ($total_pages > 1): ?>
    <div class="pagination">
        <?php if ($page > 1): ?>
            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">上一页</a>
        <?php endif; ?>
        
        <?php
        $start_page = max(1, $page - 5);
        $end_page = min($total_pages, $page + 5);
        
        for ($i = $start_page; $i <= $end_page; $i++):
        ?>
            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
               class="<?php echo $i == $page ? 'current' : ''; ?>">
                <?php echo $i; ?>
            </a>
        <?php endfor; ?>
        
        <?php if ($page < $total_pages): ?>
            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">下一页</a>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php endif; ?>

</body>
</html>
