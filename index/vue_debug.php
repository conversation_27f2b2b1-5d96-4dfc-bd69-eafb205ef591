<?php
/**
 * Vue实例调试页面
 */

// 引入公共配置文件
include("../confing/common.php");

// 检查登录状态和权限
if ($islogin != 1 || $userrow['uid'] != 1) {
    die('仅超级管理员可访问此页面');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Vue实例调试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        pre { background: #fff; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <h1>🔍 Vue实例调试工具</h1>
    
    <div class="debug-section">
        <h3>系统信息</h3>
        <p><strong>当前页面路径：</strong> <?php echo __FILE__; ?></p>
        <p><strong>相对路径：</strong> index/vue_debug.php</p>
        <p><strong>用户权限：</strong> 超级管理员 (UID: <?php echo $userrow['uid']; ?>)</p>
        <p><strong>登录状态：</strong> <?php echo $islogin == 1 ? '已登录' : '未登录'; ?></p>
    </div>
    
    <div class="debug-section">
        <h3>检查Vue实例</h3>
        <button class="btn" onclick="checkVueInstance()">检查Vue实例</button>
        <button class="btn" onclick="testAddPage()">测试添加页面</button>
        <button class="btn" onclick="testSelectMenu()">测试selectMenu</button>
        <pre id="vue-info">点击按钮开始检查...</pre>
    </div>

    <div class="debug-section">
        <h3>手动添加安全监控页面</h3>
        <button class="btn" onclick="addSecurityPage('simple')">添加简化版监控</button>
        <button class="btn" onclick="addSecurityPage('monitor')">添加完整版监控</button>
        <button class="btn" onclick="addSecurityPage('logs')">添加日志查看</button>
        <button class="btn" onclick="addSecurityPage('config')">添加配置管理</button>
        <pre id="add-result">等待操作...</pre>
    </div>

    <div class="debug-section">
        <h3>页面列表</h3>
        <button class="btn" onclick="showPageList()">显示当前页面列表</button>
        <pre id="page-list">等待操作...</pre>
    </div>

    <div class="debug-section">
        <h3>菜单系统测试</h3>
        <button class="btn" onclick="testMenuSystem()">测试菜单系统</button>
        <button class="btn" onclick="simulateMenuClick()">模拟菜单点击</button>
        <pre id="menu-test">等待操作...</pre>
    </div>

    <script>
        function log(message) {
            console.log(message);
            return message;
        }

        function checkVueInstance() {
            const info = document.getElementById('vue-info');
            let result = '';
            
            try {
                // 检查window.sp
                if (typeof window.sp !== 'undefined') {
                    result += '✅ window.sp 存在\n';
                    result += `类型: ${typeof window.sp}\n`;
                    
                    if (window.sp.selectMenu) {
                        result += '✅ selectMenu 方法存在\n';
                    } else {
                        result += '❌ selectMenu 方法不存在\n';
                    }
                    
                    if (window.sp.addPage) {
                        result += '✅ addPage 方法存在\n';
                    } else {
                        result += '❌ addPage 方法不存在\n';
                    }
                    
                    if (window.sp.showPage) {
                        result += '✅ showPage 方法存在\n';
                    } else {
                        result += '❌ showPage 方法不存在\n';
                    }
                    
                    if (window.sp.pageList) {
                        result += `✅ pageList 存在，当前页面数: ${window.sp.pageList.length}\n`;
                    } else {
                        result += '❌ pageList 不存在\n';
                    }
                    
                } else {
                    result += '❌ window.sp 不存在\n';
                }
                
                // 检查其他可能的Vue实例
                if (typeof window.vm !== 'undefined') {
                    result += '✅ window.vm 存在\n';
                } else {
                    result += '❌ window.vm 不存在\n';
                }
                
                // 检查Vue
                if (typeof window.Vue !== 'undefined') {
                    result += '✅ Vue 库已加载\n';
                } else {
                    result += '❌ Vue 库未加载\n';
                }
                
                // 检查父窗口
                if (window.parent && window.parent !== window) {
                    result += '✅ 在iframe中运行\n';
                    if (window.parent.sp) {
                        result += '✅ 父窗口的sp实例存在\n';
                    } else {
                        result += '❌ 父窗口的sp实例不存在\n';
                    }
                } else {
                    result += '❌ 不在iframe中运行\n';
                }
                
            } catch (e) {
                result += `❌ 检查过程中出错: ${e.message}\n`;
            }
            
            info.textContent = result;
        }

        function testAddPage() {
            const info = document.getElementById('vue-info');
            
            try {
                const sp = window.parent && window.parent.sp ? window.parent.sp : window.sp;
                
                if (sp && sp.addPage) {
                    const testPage = {
                        id: 'test_page_' + Date.now(),
                        name: '测试页面',
                        url: 'vue_debug.php'
                    };
                    
                    sp.addPage(testPage);
                    info.textContent = '✅ 测试页面添加成功！\n页面ID: ' + testPage.id;
                } else {
                    info.textContent = '❌ addPage 方法不可用';
                }
            } catch (e) {
                info.textContent = `❌ 添加页面失败: ${e.message}`;
            }
        }

        function testSelectMenu() {
            const info = document.getElementById('vue-info');
            
            try {
                const sp = window.parent && window.parent.sp ? window.parent.sp : window.sp;
                
                if (sp && sp.selectMenu) {
                    // 尝试调用selectMenu
                    sp.selectMenu('vue_debug');
                    info.textContent = '✅ selectMenu 调用成功';
                } else {
                    info.textContent = '❌ selectMenu 方法不可用';
                }
            } catch (e) {
                info.textContent = `❌ selectMenu 调用失败: ${e.message}`;
            }
        }

        function addSecurityPage(type) {
            const result = document.getElementById('add-result');
            
            try {
                const sp = window.parent && window.parent.sp ? window.parent.sp : window.sp;
                
                if (!sp || !sp.addPage || !sp.showPage) {
                    result.textContent = '❌ Vue实例方法不可用';
                    return;
                }
                
                let page = {};
                
                switch(type) {
                    case 'simple':
                        page = {
                            id: 'security_simple_' + Date.now(),
                            name: '安全监控（简化版）',
                            url: 'security_simple.php'
                        };
                        break;
                    case 'monitor':
                        page = {
                            id: 'security_monitor_' + Date.now(),
                            name: '安全监控面板',
                            url: 'security_monitor.php'
                        };
                        break;
                    case 'logs':
                        page = {
                            id: 'security_logs_' + Date.now(),
                            name: '安全日志查看',
                            url: 'security_logs.php'
                        };
                        break;
                    case 'config':
                        page = {
                            id: 'security_config_' + Date.now(),
                            name: '安全配置管理',
                            url: 'security_config.php'
                        };
                        break;
                }
                
                sp.addPage(page);
                sp.showPage(page);
                
                result.textContent = `✅ ${page.name} 添加成功！\n页面ID: ${page.id}`;
                
            } catch (e) {
                result.textContent = `❌ 添加失败: ${e.message}`;
            }
        }

        function showPageList() {
            const list = document.getElementById('page-list');
            
            try {
                const sp = window.parent && window.parent.sp ? window.parent.sp : window.sp;
                
                if (sp && sp.pageList) {
                    let result = `当前页面总数: ${sp.pageList.length}\n\n`;
                    
                    sp.pageList.forEach((page, index) => {
                        result += `${index + 1}. ${page.name}\n`;
                        result += `   ID: ${page.id}\n`;
                        result += `   URL: ${page.url}\n\n`;
                    });
                    
                    list.textContent = result;
                } else {
                    list.textContent = '❌ pageList 不可用';
                }
            } catch (e) {
                list.textContent = `❌ 获取页面列表失败: ${e.message}`;
            }
        }

        function testMenuSystem() {
            const result = document.getElementById('menu-test');
            
            try {
                let info = '菜单系统测试结果：\n\n';
                
                // 检查当前页面是否在iframe中
                if (window.parent && window.parent !== window) {
                    info += '✅ 当前页面在iframe中运行\n';
                    
                    // 检查父页面的菜单元素
                    const parentDoc = window.parent.document;
                    const menuItems = parentDoc.querySelectorAll('.el-menu-item');
                    info += `✅ 找到 ${menuItems.length} 个菜单项\n`;
                    
                    // 查找安全监控相关菜单
                    const securityMenus = Array.from(menuItems).filter(item => 
                        item.getAttribute('index') && item.getAttribute('index').startsWith('security_')
                    );
                    info += `✅ 找到 ${securityMenus.length} 个安全监控菜单项\n`;
                    
                    securityMenus.forEach(menu => {
                        info += `   - ${menu.getAttribute('index')}: ${menu.textContent.trim()}\n`;
                    });
                    
                } else {
                    info += '❌ 当前页面不在iframe中运行\n';
                }
                
                result.textContent = info;
                
            } catch (e) {
                result.textContent = `❌ 测试失败: ${e.message}`;
            }
        }

        function simulateMenuClick() {
            const result = document.getElementById('menu-test');
            
            try {
                if (window.parent && window.parent !== window) {
                    const parentDoc = window.parent.document;
                    const securityMenu = parentDoc.querySelector('[index="security_simple"]');
                    
                    if (securityMenu) {
                        securityMenu.click();
                        result.textContent = '✅ 模拟点击安全监控菜单成功';
                    } else {
                        result.textContent = '❌ 未找到安全监控菜单项';
                    }
                } else {
                    result.textContent = '❌ 无法访问父页面';
                }
            } catch (e) {
                result.textContent = `❌ 模拟点击失败: ${e.message}`;
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', function() {
            setTimeout(checkVueInstance, 1000);
        });
    </script>

</body>
</html>
