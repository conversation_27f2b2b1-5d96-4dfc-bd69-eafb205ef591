<?php
/**
 * 安全监控面板
 * 实时监控充值安全状况
 */

// 引入公共配置文件
include("../confing/common.php");

// 检查登录状态和权限
if ($islogin != 1 || $userrow['uid'] != 1) {
    die('仅超级管理员可访问此页面');
}

// 检查安全日志表是否存在
try {
    $table_check = $DB->query("SHOW TABLES LIKE 'qingka_wangke_security_log'");
    if (!$DB->fetch($table_check)) {
        // 自动创建安全日志表
        $create_sql = "CREATE TABLE `qingka_wangke_security_log` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `event_type` VARCHAR(32) NOT NULL,
            `ip` VARCHAR(45) NOT NULL,
            `user_agent` TEXT,
            `request_data` TEXT,
            `result` VARCHAR(16) NOT NULL,
            `details` TEXT,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX `idx_event_type` (`event_type`),
            INDEX `idx_created` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $DB->query($create_sql);
    }
} catch (Exception $e) {
    // 如果表不存在且无法创建，显示提示
    $table_error = "安全日志表不存在，请先执行数据库初始化脚本";
}

// 处理AJAX请求
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_GET['action']) {
            case 'get_recent_logs':
                $logs = [];
                if (isset($table_error)) {
                    echo json_encode(['success' => false, 'error' => $table_error]);
                    exit;
                }
                
                $result = $DB->query("SELECT * FROM qingka_wangke_security_log ORDER BY created_at DESC LIMIT 20");
                while ($row = $DB->fetch($result)) {
                    $logs[] = $row;
                }
                echo json_encode(['success' => true, 'data' => $logs]);
                exit;
                
            case 'get_stats':
                if (isset($table_error)) {
                    echo json_encode(['success' => false, 'error' => $table_error]);
                    exit;
                }
                
                $stats = [];
                
                // 今日安全事件统计
                $today = date('Y-m-d');
                $stats['today_events'] = $DB->count("SELECT COUNT(*) FROM qingka_wangke_security_log WHERE DATE(created_at) = '$today'");
                $stats['today_success'] = $DB->count("SELECT COUNT(*) FROM qingka_wangke_security_log WHERE DATE(created_at) = '$today' AND result = 'success'");
                $stats['today_failed'] = $DB->count("SELECT COUNT(*) FROM qingka_wangke_security_log WHERE DATE(created_at) = '$today' AND result = 'fail'");
                
                // 最近1小时事件
                $stats['hour_events'] = $DB->count("SELECT COUNT(*) FROM qingka_wangke_security_log WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
                
                echo json_encode(['success' => true, 'data' => $stats]);
                exit;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '数据库查询失败：' . $e->getMessage()]);
        exit;
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>安全监控面板</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { color: #666; font-size: 0.9em; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
        .info { color: #17a2b8; }
        
        .log-panel { background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .log-header { padding: 20px; border-bottom: 1px solid #eee; }
        .log-content { max-height: 500px; overflow-y: auto; }
        .log-item { padding: 15px 20px; border-bottom: 1px solid #f0f0f0; }
        .log-item:hover { background: #f8f9fa; }
        .log-time { color: #666; font-size: 0.85em; }
        .log-event { font-weight: bold; margin: 5px 0; }
        .log-details { color: #666; font-size: 0.9em; }
        
        .status-indicator { display: inline-block; width: 8px; height: 8px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-fail { background: #dc3545; }
        .status-warning { background: #ffc107; }
        
        .refresh-btn { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }
        .refresh-btn:hover { background: #0056b3; }
        
        .auto-refresh { margin-left: 10px; }
        .auto-refresh input { margin-right: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ 安全监控面板</h1>
            <p>实时监控充值系统安全状况</p>
            <button class="refresh-btn" onclick="refreshData()">刷新数据</button>
            <label class="auto-refresh">
                <input type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()"> 自动刷新 (30秒)
            </label>
        </div>

        <?php if (isset($table_error)): ?>
        <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h3>⚠️ 数据库表不存在</h3>
            <p><?php echo $table_error; ?></p>
        </div>
        <?php endif; ?>

        <div class="stats-grid" id="statsGrid">
            <!-- 统计数据将通过JavaScript加载 -->
        </div>

        <div class="log-panel">
            <div class="log-header">
                <h3>📋 最近安全事件</h3>
            </div>
            <div class="log-content" id="logContent">
                <!-- 日志数据将通过JavaScript加载 -->
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
        });

        // 刷新数据
        function refreshData() {
            loadStats();
            loadLogs();
        }

        // 加载统计数据
        function loadStats() {
            fetch('?action=get_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderStats(data.data);
                    } else {
                        document.getElementById('statsGrid').innerHTML = 
                            '<div style="grid-column: 1/-1; background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;">' +
                            '错误：' + (data.error || '未知错误') + '</div>';
                    }
                })
                .catch(error => {
                    console.error('加载统计数据失败:', error);
                    document.getElementById('statsGrid').innerHTML = 
                        '<div style="grid-column: 1/-1; background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;">' +
                        '加载统计数据失败</div>';
                });
        }

        // 加载日志数据
        function loadLogs() {
            fetch('?action=get_recent_logs')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderLogs(data.data);
                    } else {
                        document.getElementById('logContent').innerHTML = 
                            '<div style="padding: 20px; color: #721c24;">错误：' + (data.error || '未知错误') + '</div>';
                    }
                })
                .catch(error => {
                    console.error('加载日志数据失败:', error);
                    document.getElementById('logContent').innerHTML = 
                        '<div style="padding: 20px; color: #721c24;">加载日志数据失败</div>';
                });
        }

        // 渲染统计数据
        function renderStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            
            const successRate = stats.today_events > 0 ? 
                ((stats.today_success / stats.today_events) * 100).toFixed(1) : 0;
            
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number info">${stats.today_events}</div>
                    <div class="stat-label">今日总事件</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number success">${stats.today_success}</div>
                    <div class="stat-label">今日成功事件</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number danger">${stats.today_failed}</div>
                    <div class="stat-label">今日失败事件</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number warning">${stats.hour_events}</div>
                    <div class="stat-label">最近1小时事件</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number ${successRate >= 95 ? 'success' : successRate >= 90 ? 'warning' : 'danger'}">${successRate}%</div>
                    <div class="stat-label">今日成功率</div>
                </div>
            `;
        }

        // 渲染日志数据
        function renderLogs(logs) {
            const logContent = document.getElementById('logContent');
            
            if (logs.length === 0) {
                logContent.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">暂无安全事件记录</div>';
                return;
            }
            
            logContent.innerHTML = logs.map(log => {
                const statusClass = log.result === 'success' ? 'status-success' : 
                                  log.result === 'fail' ? 'status-fail' : 'status-warning';
                
                return `
                    <div class="log-item">
                        <div class="log-time">${log.created_at}</div>
                        <div class="log-event">
                            <span class="status-indicator ${statusClass}"></span>
                            ${log.event_type} - ${log.result}
                        </div>
                        <div class="log-details">
                            IP: ${log.ip} | ${log.details || '无详细信息'}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            
            if (checkbox.checked) {
                autoRefreshInterval = setInterval(refreshData, 30000); // 30秒刷新一次
            } else {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
            }
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
