<?php
/**
 * 天河(Skyriver)自动同步集合脚本
 * 功能：商品价格同步 + 订单进度同步
 * 适用于宝塔计划任务
 * 
 * 使用方法：
 * 1. 商品同步：每1小时执行一次
 * 2. 进度同步：每5-10分钟执行一次
 * 
 * 计划任务设置：
 * 商品同步：0 * * * * /usr/bin/php /www/wwwroot/117.72.158.75/tianhe_auto_sync.php product
 * 进度同步：每5分钟 /usr/bin/php /www/wwwroot/117.72.158.75/tianhe_auto_sync.php progress
 * 
 * 作者: AI Assistant
 * 创建时间: 2025-09-01
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 生产环境关闭错误显示
ini_set('log_errors', 1);

// 引入公共配置
include(__DIR__ . '/confing/common.php');

// 加载天河配置文件
$tianhe_config_file = __DIR__ . '/tianhe_config.php';
if (file_exists($tianhe_config_file)) {
    $user_config = include($tianhe_config_file);
} else {
    $user_config = [];
}

// 默认配置
$default_config = [
    'tianhe_hid' => 52,
    'price_multiplier' => 1.5,
    'skip_categories' => ['1'],
    'sync_product_enabled' => true,
    'sync_progress_enabled' => true,
    'max_progress_batch' => 5000,
    'sync_interval' => 300,
    'enable_logging' => true,
    'log_level' => 'INFO',
    'max_execution_time' => 300,
    'memory_limit' => '256M',
    'api_timeout' => 30,
    'api_retry_times' => 3,
    'api_retry_delay' => 2,
];

// 合并配置
$config = array_merge($default_config, $user_config);

// 添加运行时配置
$config['log_file'] = __DIR__ . '/logs/tianhe_sync.log';
$config['timestamp_file'] = __DIR__ . '/logs/tianhe_last_sync.dat';

// 设置PHP配置
ini_set('max_execution_time', $config['max_execution_time']);
ini_set('memory_limit', $config['memory_limit']);

// 创建日志目录
$log_dir = dirname($config['log_file']);
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

/**
 * 写入日志
 */
function writeLog($message, $level = 'INFO') {
    global $config;

    if (!$config['enable_logging']) {
        return;
    }

    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;

    // 写入日志文件
    @file_put_contents($config['log_file'], $log_message, FILE_APPEND | LOCK_EX);

    // 同时输出到控制台（用于调试）
    if (php_sapi_name() === 'cli') {
        echo $log_message;
    }

    // 清理旧日志
    cleanOldLogs();
}

/**
 * 清理旧日志文件
 */
function cleanOldLogs() {
    global $config;
    static $last_cleanup = 0;

    // 每小时只清理一次
    if (time() - $last_cleanup < 3600) {
        return;
    }

    $log_dir = dirname($config['log_file']);
    $retention_days = $config['log_retention_days'] ?? 7;
    $cutoff_time = time() - ($retention_days * 24 * 3600);

    if (is_dir($log_dir)) {
        $files = glob($log_dir . '/*.log');
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff_time) {
                @unlink($file);
            }
        }
    }

    $last_cleanup = time();
}

/**
 * 带重试的API请求
 */
function apiRequest($url, $data, $config) {
    $retry_times = $config['api_retry_times'];
    $retry_delay = $config['api_retry_delay'];

    for ($i = 0; $i <= $retry_times; $i++) {
        try {
            $response = get_url($url, $data);
            if ($response !== false && !empty($response)) {
                return $response;
            }
        } catch (Exception $e) {
            writeLog("API请求失败 (尝试 " . ($i + 1) . "): " . $e->getMessage(), 'WARN');
        }

        if ($i < $retry_times) {
            sleep($retry_delay);
        }
    }

    throw new Exception("API请求失败，已重试 {$retry_times} 次");
}

/**
 * 获取天河接口配置
 */
function getTianheConfig($DB, $hid) {
    $config = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$hid}'");
    if (!$config) {
        throw new Exception("未找到天河接口配置 (HID: {$hid})");
    }
    return $config;
}

/**
 * 商品价格同步（原有功能）
 */
function syncProducts($DB, $config) {
    if (!$config['sync_product_enabled']) {
        writeLog("商品同步已禁用，跳过");
        return true;
    }

    writeLog("开始天河商品价格同步...");
    $start_time = microtime(true);

    try {
        // 获取天河接口配置
        $tianhe_config = getTianheConfig($DB, $config['tianhe_hid']);

        // 构建API请求
        $api_url = $tianhe_config['url'] . '/api.php?act=getclass';
        $post_data = [
            'uid' => $tianhe_config['user'],
            'key' => $tianhe_config['pass']
        ];

        writeLog("请求天河API: {$api_url}");

        // 使用带重试的API请求
        $response = apiRequest($api_url, $post_data, $config);
        if (!$response) {
            throw new Exception("天河API请求失败");
        }
        
        $result = json_decode($response, true);
        if (!$result || !isset($result['data'])) {
            throw new Exception("天河API返回数据格式错误: " . substr($response, 0, 200));
        }
        
        $updated_count = 0;
        $skipped_count = 0;
        $error_count = 0;
        
        foreach ($result['data'] as $item) {
            try {
                // 检查是否跳过该分类
                if (in_array($item['fenlei'], $config['skip_categories'])) {
                    $skipped_count++;
                    continue;
                }
                
                // 计算价格
                $price = floatval($item['price']) * $config['price_multiplier'];
                
                // 转义内容，防止SQL注入
                $content = $DB->escape($item['content']);
                
                // 更新商品信息
                $update_sql = "UPDATE qingka_wangke_class 
                              SET price='{$price}', content='{$content}' 
                              WHERE docking='{$config['tianhe_hid']}' AND noun='{$item['cid']}'";
                
                $result = $DB->query($update_sql);
                if ($result) {
                    $updated_count++;
                } else {
                    $error_count++;
                    writeLog("更新商品失败: CID={$item['cid']}", 'ERROR');
                }
                
            } catch (Exception $e) {
                $error_count++;
                writeLog("处理商品时出错: " . $e->getMessage(), 'ERROR');
            }
        }
        
        $execution_time = round(microtime(true) - $start_time, 2);
        writeLog("商品同步完成 - 更新: {$updated_count}, 跳过: {$skipped_count}, 错误: {$error_count}, 耗时: {$execution_time}秒");

        // 性能监控
        if ($config['enable_performance_monitoring']) {
            writeLog("性能统计 - 平均每个商品处理时间: " . round($execution_time / max(1, $updated_count + $skipped_count), 4) . "秒");
        }

        // 如果有商品更新，清理缓存
        if ($updated_count > 0) {
            clearProductCache($redis, $config);
        }

        return true;

    } catch (Exception $e) {
        $execution_time = round(microtime(true) - $start_time, 2);
        writeLog("商品同步失败: " . $e->getMessage() . " (耗时: {$execution_time}秒)", 'ERROR');
        return false;
    }
}

/**
 * 商品克隆同步（上下架、分类映射）
 */
function syncProductClone($DB, $config) {
    if (!$config['sync_product_clone_enabled']) {
        writeLog("商品克隆同步已禁用，跳过");
        return true;
    }

    writeLog("开始天河商品克隆同步（上下架）...");
    $start_time = microtime(true);

    try {
        // 获取天河接口配置
        $tianhe_config = getTianheConfig($DB, $config['tianhe_hid']);

        // 构建API请求
        $api_url = $tianhe_config['url'] . '/api.php?act=getclass';
        $post_data = [
            'uid' => $tianhe_config['user'],
            'key' => $tianhe_config['pass']
        ];

        writeLog("请求天河API获取商品列表: {$api_url}");

        // 使用带重试的API请求
        $response = apiRequest($api_url, $post_data, $config);
        if (!$response) {
            throw new Exception("天河API请求失败");
        }

        $result = json_decode($response, true);
        if (!$result || !isset($result['data'])) {
            throw new Exception("天河API返回数据格式错误: " . substr($response, 0, 200));
        }

        $categories = $result['data'];
        $numItemsInserted = 0;
        $numItemsUpdated = 0;
        $numItemsDeleted = 0;

        // 获取本地现有商品
        $localProducts = $DB->query("SELECT noun, name FROM qingka_wangke_class WHERE docking='{$config['tianhe_hid']}'");
        $upstreamProductIds = array_map(function($category) {
            return $category['cid'];
        }, $categories);

        // 处理本地不存在于上游的商品（下架或删除）
        while ($row = $DB->fetch($localProducts)) {
            if (!in_array($row['noun'], $upstreamProductIds)) {
                if ($config['soft_delete_products']) {
                    // 软删除：设置status=0（推荐方式，避免数据丢失）
                    $DB->query("UPDATE qingka_wangke_class SET status = 0 WHERE noun='{$row['noun']}' AND docking='{$config['tianhe_hid']}'");
                    writeLog("下架商品: {$row['name']}");
                } else {
                    // 硬删除：直接删除记录
                    $DB->query("DELETE FROM qingka_wangke_class WHERE noun='{$row['noun']}' AND docking='{$config['tianhe_hid']}'");
                    writeLog("删除商品: {$row['name']}");
                }
                $numItemsDeleted++;
            }
        }

        // 处理分类映射
        $category_mapping = $config['category_mapping'] ?? [];
        $default_category = '113'; // 杂分类

        // 同步分类信息（如果启用）
        if ($config['clone_categories']) {
            $processed_categories = [];
            foreach ($categories as $value) {
                if (in_array($value['fenlei'], $config['skip_categories'])) {
                    continue;
                }

                // 确定本地分类ID
                $local_category_id = isset($category_mapping[$value['fenlei']])
                    ? $category_mapping[$value['fenlei']]
                    : $default_category;

                if (!in_array($local_category_id, $processed_categories)) {
                    $existingCategory = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE id='{$local_category_id}'");
                    if (!$existingCategory) {
                        // 获取分类名称
                        $category_name = isset($category_mapping[$value['fenlei']])
                            ? getCategoryNameById($local_category_id)
                            : '杂';

                        $DB->query("INSERT INTO qingka_wangke_fenlei (id, sort, name, status, time) VALUES ('{$local_category_id}', '0', '{$category_name}', '1', NOW())");
                        writeLog("创建分类: {$category_name} (ID: {$local_category_id})");
                    }
                    $processed_categories[] = $local_category_id;
                }
            }
        }

        // 同步商品
        foreach ($categories as $value) {
            if (in_array($value['fenlei'], $config['skip_categories'])) {
                continue;
            }

            // 确定本地分类ID
            $local_category_id = isset($category_mapping[$value['fenlei']])
                ? $category_mapping[$value['fenlei']]
                : $default_category;

            $price = floatval($value['price']) * $config['price_multiplier'];
            $cid = $DB->escape($value['cid']);
            $name = $DB->escape($value['name']);
            $content = $DB->escape($value['content']);
            $sort = intval($value['sort']);
            $status = intval($value['status']);

            $existingProduct = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking='{$config['tianhe_hid']}' AND noun='{$cid}'");

            if ($existingProduct) {
                // 更新现有商品（包括重新上架逻辑）
                $update_sql = "UPDATE qingka_wangke_class
                              SET price='{$price}',
                                  content='{$content}',
                                  name='{$name}',
                                  sort='{$sort}',
                                  status='{$status}',
                                  fenlei='{$local_category_id}'
                              WHERE docking='{$config['tianhe_hid']}' AND noun='{$cid}'";

                if ($DB->query($update_sql)) {
                    $numItemsUpdated++;
                    // 如果商品从下架状态变为上架状态，记录日志
                    if ($existingProduct['status'] == 0 && $status == 1) {
                        writeLog("重新上架商品: {$name} (CID: {$cid})");
                    }
                } else {
                    writeLog("更新商品失败: CID={$cid}, 名称={$name}", 'ERROR');
                }
            } else {
                // 插入新商品 - 不指定cid字段，让它自增
                $insert_sql = "INSERT INTO qingka_wangke_class
                              (name, getnoun, noun, fenlei, queryplat, docking, price, sort, content, addtime, status)
                              VALUES ('{$name}', '{$cid}', '{$cid}',
                                     '{$local_category_id}', '{$config['tianhe_hid']}', '{$config['tianhe_hid']}',
                                     '{$price}', '{$sort}', '{$content}', NOW(), '{$status}')";

                if ($DB->query($insert_sql)) {
                    $numItemsInserted++;
                    writeLog("新增商品: {$name} (CID: {$cid}) -> 分类{$local_category_id}");
                } else {
                    writeLog("插入商品失败: CID={$cid}, 名称={$name}, SQL错误: " . $DB->error, 'ERROR');
                }
            }
        }

        $execution_time = round(microtime(true) - $start_time, 2);
        writeLog("商品克隆同步完成 - 新增: {$numItemsInserted}, 更新: {$numItemsUpdated}, 下架: {$numItemsDeleted}, 耗时: {$execution_time}秒");

        // 清理相关缓存，确保前端能立即看到新商品
        if ($numItemsInserted > 0 || $numItemsUpdated > 0 || $numItemsDeleted > 0) {
            clearProductCache($redis, $config);
        }

        return true;

    } catch (Exception $e) {
        $execution_time = round(microtime(true) - $start_time, 2);
        writeLog("商品克隆同步失败: " . $e->getMessage() . " (耗时: {$execution_time}秒)", 'ERROR');
        return false;
    }
}

/**
 * 获取分类名称
 */
function getCategoryNameById($category_id) {
    $names = [
        '111' => '继教4',
        '106' => '易教育',
        '104' => '8090edu',
        '112' => '学习通',
        '113' => '杂'
    ];
    return isset($names[$category_id]) ? $names[$category_id] : '未知分类';
}

/**
 * 清理商品相关缓存（增强版）
 */
function clearProductCache($redis, $config) {
    try {
        writeLog("开始清理商品缓存...");

        // 清理所有商品列表相关缓存
        $patterns = [
            'class_list_v2_*',
            'class_list_*',
            'category_v2_*',
            'category_*'
        ];

        $total_cleared = 0;

        foreach ($patterns as $pattern) {
            $keys = $redis->keys($pattern);
            if ($keys && is_array($keys)) {
                foreach ($keys as $key) {
                    if ($redis->del($key)) {
                        $total_cleared++;
                    }
                }
            }
        }

        // 设置缓存版本号和强制刷新标记
        $cache_version = time();
        $redis->set('cache_version', $cache_version);
        $redis->set('force_refresh_flag', '1', 1800); // 30分钟强制刷新
        $redis->set('last_sync_time', date('Y-m-d H:i:s'));

        writeLog("缓存清理完成，已清理 {$total_cleared} 个缓存键，缓存版本: {$cache_version}");

    } catch (Exception $e) {
        writeLog("缓存清理失败: " . $e->getMessage(), 'ERROR');
    }
}

/**
 * 订单进度同步
 */
function syncProgress($DB, $redis, $config) {
    if (!$config['sync_progress_enabled']) {
        writeLog("进度同步已禁用，跳过");
        return true;
    }

    writeLog("开始天河订单进度同步...");
    $start_time = microtime(true);

    try {
        // 获取天河接口配置
        $tianhe_config = getTianheConfig($DB, $config['tianhe_hid']);

        // 获取上次同步时间
        $last_timestamp = getLastSyncTimestamp($config['timestamp_file']);
        if ($last_timestamp === null) {
            writeLog("跳过本次进度同步（频率限制）");
            return true;
        }

        writeLog("上次同步时间: " . date('Y-m-d H:i:s', $last_timestamp));

        // 更新时间戳文件
        file_put_contents($config['timestamp_file'], time());
        
        $offset = 0;
        $total_synced = 0;
        
        while (true) {
            // 构建进度同步请求
            $api_url = 'http://skyriver.top/api.php?act=pljd';
            $post_data = [
                'uid' => $tianhe_config['user'],
                'key' => $tianhe_config['pass'],
                'offset' => $offset,
                'timestamp' => $last_timestamp
            ];
            
            // 使用带重试的API请求
            try {
                $response = apiRequest($api_url, $post_data, $config);
            } catch (Exception $e) {
                writeLog("天河进度API请求失败: " . $e->getMessage(), 'ERROR');
                break;
            }
            
            $result = json_decode($response, true);
            if (!$result) {
                writeLog("天河进度API返回数据格式错误: JSON解析失败", 'ERROR');
                break;
            }

            // 检查API返回的错误码
            if (isset($result['code']) && $result['code'] != 1) {
                $error_msg = isset($result['msg']) ? $result['msg'] : '未知错误';
                if ($result['code'] == -2) {
                    writeLog("天河API频率限制: {$error_msg}，跳过本次同步", 'WARN');
                    return true; // 频率限制不算错误，返回成功
                } else {
                    writeLog("天河API返回错误 (code: {$result['code']}): {$error_msg}", 'ERROR');
                    break;
                }
            }

            if (!isset($result['data'])) {
                writeLog("天河进度API返回数据格式错误: 缺少data字段", 'ERROR');
                break;
            }
            
            $batch_data = $result['data'];
            $batch_count = count($batch_data);
            
            if ($batch_count == 0) {
                break;
            }
            
            // 批量更新订单进度
            $updated_in_batch = 0;
            foreach ($batch_data as $order) {
                try {
                    // 转义数据
                    $yid = $DB->escape($order['id']);
                    $status = $DB->escape($order['status']);
                    $remarks = $DB->escape($order['remarks']);
                    $process = $DB->escape($order['process']);
                    $user = $DB->escape($order['user']);
                    $pass = $DB->escape($order['pass']);
                    $kcname = $DB->escape($order['kcname']);
                    
                    // 处理时间字段
                    $courseStartTime = isset($order['kcks']) ? $DB->escape($order['kcks']) : '';
                    $courseEndTime = isset($order['kcjs']) ? $DB->escape($order['kcjs']) : '';
                    $examStartTime = isset($order['ksks']) ? $DB->escape($order['ksks']) : '';
                    $examEndTime = isset($order['ksjs']) ? $DB->escape($order['ksjs']) : '';
                    
                    // 更新订单
                    $update_sql = "UPDATE qingka_wangke_order 
                                  SET yid='{$yid}', 
                                      status='{$status}', 
                                      remarks='{$remarks}', 
                                      process='{$process}', 
                                      courseStartTime='{$courseStartTime}', 
                                      courseEndTime='{$courseEndTime}', 
                                      examStartTime='{$examStartTime}', 
                                      examEndTime='{$examEndTime}' 
                                  WHERE user='{$user}' AND pass='{$pass}' AND kcname='{$kcname}' 
                                  AND hid='{$config['tianhe_hid']}'";
                    
                    if ($DB->query($update_sql)) {
                        $updated_in_batch++;
                    }
                    
                } catch (Exception $e) {
                    writeLog("更新订单进度失败: " . $e->getMessage(), 'ERROR');
                }
            }
            
            $total_synced += $updated_in_batch;
            writeLog("批次同步完成: {$updated_in_batch}/{$batch_count} 条记录");
            
            $offset += $config['max_progress_batch'];
            
            // 如果返回的数据少于批次大小，说明已经同步完成
            if ($batch_count < $config['max_progress_batch']) {
                break;
            }
            
            // 防止过度请求，稍作延迟
            usleep(100000); // 0.1秒
        }
        
        $execution_time = round(microtime(true) - $start_time, 2);
        writeLog("进度同步完成 - 总计更新: {$total_synced} 条订单, 耗时: {$execution_time}秒");

        // 性能监控
        if ($config['enable_performance_monitoring'] && $total_synced > 0) {
            writeLog("性能统计 - 平均每个订单处理时间: " . round($execution_time / $total_synced, 4) . "秒");
        }

        return true;

    } catch (Exception $e) {
        $execution_time = round(microtime(true) - $start_time, 2);
        writeLog("进度同步失败: " . $e->getMessage() . " (耗时: {$execution_time}秒)", 'ERROR');
        return false;
    }
}

/**
 * 获取上次同步时间戳
 */
function getLastSyncTimestamp($timestamp_file) {
    if (!file_exists($timestamp_file)) {
        return time() - 300; // 默认5分钟前
    }

    $last_time = intval(file_get_contents($timestamp_file));
    $current_time = time();

    // 确保至少间隔3分钟（天河API限制）
    if ($current_time - $last_time < 180) {
        writeLog("距离上次同步时间不足3分钟，跳过本次同步", 'INFO');
        return null; // 返回null表示跳过
    }

    return $last_time - 60; // 减去1分钟，确保不遗漏数据
}

/**
 * 主函数
 */
function main() {
    global $DB, $redis, $config, $argv;

    // 获取命令行参数
    $action = isset($argv[1]) ? $argv[1] : 'help';
    
    writeLog("天河同步脚本启动 - 操作: {$action}");
    
    try {
        switch ($action) {
            case 'product':
            case 'products':
                $success = syncProducts($DB, $config);
                exit($success ? 0 : 1);

            case 'clone':
            case 'product-clone':
                $success = syncProductClone($DB, $config);
                exit($success ? 0 : 1);

            case 'progress':
                $success = syncProgress($DB, $redis, $config);
                exit($success ? 0 : 1);

            case 'all':
                $product_success = syncProducts($DB, $config);
                sleep(2); // 间隔2秒
                $clone_success = syncProductClone($DB, $config);
                sleep(2); // 间隔2秒
                $progress_success = syncProgress($DB, $redis, $config);
                exit(($product_success && $clone_success && $progress_success) ? 0 : 1);

            case 'daily':
                // 每日同步：商品克隆 + 进度同步
                $clone_success = syncProductClone($DB, $config);
                sleep(2);
                $progress_success = syncProgress($DB, $redis, $config);
                exit(($clone_success && $progress_success) ? 0 : 1);

            case 'help':
            default:
                echo "天河自动同步脚本使用说明:\n";
                echo "php tianhe_auto_sync.php product     - 同步商品价格和说明\n";
                echo "php tianhe_auto_sync.php clone       - 商品克隆同步（上下架+分类映射）\n";
                echo "php tianhe_auto_sync.php progress    - 同步订单进度\n";
                echo "php tianhe_auto_sync.php daily       - 每日同步（克隆+进度）\n";
                echo "php tianhe_auto_sync.php all         - 同步所有内容\n";
                echo "\n宝塔计划任务设置:\n";
                echo "商品价格同步(每小时): 0 * * * * /usr/bin/php " . __FILE__ . " product\n";
                echo "商品克隆同步(每天): 0 2 * * * /usr/bin/php " . __FILE__ . " clone\n";
                echo "进度同步(每5分钟): */5 * * * * /usr/bin/php " . __FILE__ . " progress\n";
                echo "每日同步(推荐): 0 1 * * * /usr/bin/php " . __FILE__ . " daily\n";
                exit(0);
        }
        
    } catch (Exception $e) {
        writeLog("脚本执行失败: " . $e->getMessage(), 'ERROR');
        exit(1);
    }
}

// 执行主函数
if (php_sapi_name() === 'cli') {
    main();
} else {
    // Web访问时显示帮助信息
    header('Content-Type: text/plain; charset=utf-8');
    echo "天河自动同步脚本\n";
    echo "请通过命令行执行此脚本\n";
    echo "使用方法: php tianhe_auto_sync.php [product|progress|all]\n";
}
?>
