<?php
/**
 * 天河自动同步配置文件
 * 请根据您的实际情况修改以下配置
 */

return [
    // 基本配置
    'tianhe_hid' => 52,                    // 天河接口的HID（请勿修改，除非您的HID不是52）
    'price_multiplier' => 1.5,               // 价格倍数（1表示原价，2表示2倍价格）
    
    // 商品同步配置
    'skip_categories' => ['1', '22', '9','50'], // 跳过的分类ID：天河自营(22)、新源图图(9)、skyriver(1)
    'sync_product_enabled' => true,        // 是否启用商品同步
    'sync_product_clone_enabled' => true,  // 是否启用商品克隆同步（上下架）
    'clone_categories' => true,            // 是否同步分类信息
    'soft_delete_products' => true,        // true=下架商品(status=0), false=删除商品

    // 分类映射配置（天河分类ID => 本地分类ID）
    'category_mapping' => [
        '40' => '111',  // 天河ID:40 (900+继续教育) => 继教4
        '41' => '106',  // 天河ID:41 (900+易教育) => 易教育
        '49' => '104',  // 天河ID:49 (8090继续教育) => 8090edu
        '54' => '112',  // 天河ID:54 (学妹项目) => 学习通
        // 其他所有分类 => 杂(113)，在代码中处理
    ],
    
    // 进度同步配置
    'sync_progress_enabled' => true,       // 是否启用进度同步
    'max_progress_batch' => 5000,          // 进度同步每批次最大数量
    'sync_interval' => 300,                // 进度同步间隔（秒），建议300秒（5分钟）
    'min_sync_interval' => 180,            // 最小同步间隔（秒），天河API限制3分钟
    
    // 日志配置
    'enable_logging' => true,              // 是否启用日志记录
    'log_level' => 'INFO',                 // 日志级别：DEBUG, INFO, WARN, ERROR
    'log_retention_days' => 7,             // 日志保留天数
    
    // 安全配置
    'max_execution_time' => 300,           // 脚本最大执行时间（秒）
    'memory_limit' => '256M',              // 内存限制
    
    // API配置
    'api_timeout' => 30,                   // API请求超时时间（秒）
    'api_retry_times' => 3,                // API请求失败重试次数
    'api_retry_delay' => 2,                // 重试间隔（秒）
    
    // 高级配置
    'enable_error_notification' => false,  // 是否启用错误通知（需要配置邮件或webhook）
    'notification_webhook' => '',          // 错误通知webhook地址
    'enable_performance_monitoring' => true, // 是否启用性能监控
];
?>
