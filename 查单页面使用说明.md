# 查单页面使用说明

## 📋 功能概述

我已经为您创建了两个查单功能：

1. **网页版查单页面** (`check_order.php`) - 适合直接发给客户使用
2. **API接口** (`api/check_order.php`) - 适合集成到其他系统

## 🌐 网页版查单页面

### 访问地址
```
https://您的域名/check_order.php
```

### 功能特点
- ✨ 现代化响应式设计，支持手机和电脑访问
- 🔍 简单易用的搜索界面
- 📊 直观的订单进度显示
- 🎨 美观的卡片式订单展示
- 📱 完美适配移动设备

### 使用方法
1. 客户打开页面
2. 输入学习账号
3. 点击"查询订单"按钮
4. 查看订单详情和进度

### 显示信息
- 订单编号和状态
- 学校、姓名、课程名称
- 平台信息
- 下单时间
- 课程开始/结束时间
- 完成进度（带进度条）
- 备注信息

## 🔌 API接口

### 接口地址
```
POST/GET https://您的域名/api/check_order.php
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 要查询的学习账号 |

### 请求示例

**POST请求（JSON）：**
```json
{
    "username": "学习账号"
}
```

**POST请求（表单）：**
```
username=学习账号
```

**GET请求：**
```
?username=学习账号
```

### 响应格式

**成功响应：**
```json
{
    "code": 1,
    "msg": "查询成功",
    "total": 2,
    "data": [
        {
            "id": 12345,
            "platform": "8090教育",
            "school": "某某大学",
            "name": "张三",
            "username": "zhangsan",
            "course_name": "大学英语",
            "status": "进行中",
            "progress": "75%",
            "remarks": "正常进行中",
            "order_time": "2025-01-01 10:00:00",
            "course_start_time": "2025-01-01 08:00:00",
            "course_end_time": "2025-06-30 18:00:00",
            "exam_start_time": "",
            "exam_end_time": "",
            "fees": 50.00
        }
    ]
}
```

**失败响应：**
```json
{
    "code": -1,
    "msg": "未找到该账号的订单信息",
    "data": []
}
```

### 状态码说明
- `code: 1` - 查询成功
- `code: 0` - 查询成功但无数据
- `code: -1` - 查询失败或参数错误

## 🚀 部署说明

### 1. 文件权限
确保以下文件有正确的读取权限：
```bash
chmod 644 check_order.php
chmod 644 api/check_order.php
```

### 2. 数据库连接
两个文件都使用 `confing/mysqlset.php` 中的数据库配置，确保配置正确。

### 3. 安全考虑
- API接口已添加CORS支持
- 使用预处理语句防止SQL注入
- 限制查询结果数量（网页版50条，API版100条）

## 📱 客户使用指南

### 发给客户的说明
```
亲爱的客户，您好！

您可以通过以下链接查询您的订单进度：
https://您的域名/check_order.php

使用方法：
1. 打开链接
2. 输入您的学习账号
3. 点击查询按钮
4. 查看订单状态和进度

如有任何问题，请联系客服。
```

## 🔧 自定义修改

### 修改样式
编辑 `check_order.php` 中的 `<style>` 部分可以调整页面样式。

### 修改查询条件
可以在SQL语句中添加更多筛选条件，比如按时间范围查询等。

### 添加更多字段
可以在查询中添加更多订单字段，如费用、货源信息等。

## 📞 技术支持

如需要修改功能或样式，请联系技术人员进行调整。

---

**注意事项：**
- 请确保数据库连接正常
- 建议定期备份数据库
- 如有大量查询请求，建议添加缓存机制
