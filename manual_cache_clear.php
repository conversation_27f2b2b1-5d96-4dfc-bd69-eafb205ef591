<?php
/**
 * 手动缓存清理脚本
 * 用于解决前端搜索不到新商品的问题
 */

include('confing/common.php');

echo "🧹 手动清理商品缓存\n";
echo "==================\n";

try {
    // 清理所有商品列表相关缓存
    $patterns = [
        'class_list_*',
        'category_*',
        'user_favorites:*'
    ];
    
    $total_cleared = 0;
    
    foreach ($patterns as $pattern) {
        echo "清理模式: {$pattern}\n";
        $keys = $redis->keys($pattern);
        if ($keys && is_array($keys)) {
            $count = 0;
            foreach ($keys as $key) {
                if ($redis->del($key)) {
                    $count++;
                    $total_cleared++;
                }
            }
            echo "  - 清理了 {$count} 个缓存键\n";
        } else {
            echo "  - 没有找到匹配的缓存键\n";
        }
    }
    
    // 设置缓存版本号，强制前端刷新
    $cache_version = time();
    $redis->set('cache_version', $cache_version);
    $redis->set('last_cache_clear', date('Y-m-d H:i:s'));
    
    echo "\n✅ 缓存清理完成！\n";
    echo "总共清理: {$total_cleared} 个缓存键\n";
    echo "缓存版本: {$cache_version}\n";
    echo "\n💡 提示: 用户现在可以搜索到最新的商品了\n";
    
} catch (Exception $e) {
    echo "❌ 缓存清理失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
