<?php
/**
 * 分类显示名称映射配置
 * 只修改前端显示，不影响后端功能
 * 
 * 使用方法：
 * 1. 在需要显示分类名称的地方调用 getCategoryDisplayName($original_name)
 * 2. 后端逻辑继续使用原始名称，确保功能不受影响
 */

if (!defined('IN_CRONLITE')) {
    die('Access denied');
}

/**
 * 分类显示名称映射表
 * 键：数据库中的原始名称
 * 值：前端显示的名称
 */
$category_display_mapping = [
    // 教育平台分类映射（按用户要求调整）
    'yyy' => '继教1',
    'yyy教育' => '继教1',
    '8090edu' => '继教2',
    '8090教育' => '继教2',
    '易教育' => '继教3',

    // 可以继续添加其他分类的映射
    // '原始名称' => '显示名称',
];

/**
 * 获取分类的显示名称（支持权限控制）
 * @param string $original_name 数据库中的原始分类名称
 * @return string 前端显示的分类名称
 */
function getCategoryDisplayName($original_name) {
    global $category_display_mapping, $userrow;

    // 超级管理员(uid=1)显示原始名称，便于管理和调试
    if (isset($userrow['uid']) && $userrow['uid'] == 1) {
        return $original_name;
    }

    // 普通用户显示映射后的名称
    if (isset($category_display_mapping[$original_name])) {
        return $category_display_mapping[$original_name];
    }

    // 如果没有映射配置，返回原始名称
    return $original_name;
}

/**
 * 批量获取分类显示名称
 * @param array $categories 分类数组，每个元素包含name字段
 * @return array 处理后的分类数组，name字段被替换为显示名称
 */
function processCategoriesForDisplay($categories) {
    foreach ($categories as &$category) {
        if (isset($category['name'])) {
            $category['display_name'] = getCategoryDisplayName($category['name']);
        }
    }
    return $categories;
}

/**
 * 获取反向映射（从显示名称到原始名称）
 * 主要用于调试和管理
 * @param string $display_name 显示名称
 * @return string 原始名称
 */
function getOriginalCategoryName($display_name) {
    global $category_display_mapping;
    
    $reverse_mapping = array_flip($category_display_mapping);
    
    if (isset($reverse_mapping[$display_name])) {
        return $reverse_mapping[$display_name];
    }
    
    return $display_name;
}

/**
 * 检查是否为映射的分类
 * @param string $name 分类名称（原始或显示）
 * @return bool 是否为映射的分类
 */
function isMappedCategory($name) {
    global $category_display_mapping;
    
    return isset($category_display_mapping[$name]) || in_array($name, $category_display_mapping);
}

/**
 * 获取所有映射配置
 * @return array 映射配置数组
 */
function getAllCategoryMappings() {
    global $category_display_mapping;
    return $category_display_mapping;
}

/**
 * 添加新的分类映射
 * @param string $original_name 原始名称
 * @param string $display_name 显示名称
 */
function addCategoryMapping($original_name, $display_name) {
    global $category_display_mapping;
    $category_display_mapping[$original_name] = $display_name;
}

/**
 * 移除分类映射
 * @param string $original_name 原始名称
 */
function removeCategoryMapping($original_name) {
    global $category_display_mapping;
    unset($category_display_mapping[$original_name]);
}

/**
 * 为Vue.js提供的JavaScript映射配置
 * @return string JavaScript对象字符串
 */
function getCategoryMappingForJS() {
    global $category_display_mapping;
    return json_encode($category_display_mapping, JSON_UNESCAPED_UNICODE);
}

?>
